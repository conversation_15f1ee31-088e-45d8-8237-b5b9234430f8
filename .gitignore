# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.pyc
*.pdb
*.egg
*.egg-info/
dist/
build/
.eggs/
pip-wheel-metadata/
*.manifest
*.spec
.env
.venv
env/
venv/
ENV/
*.sqlite3
*.db

# Jupyter Notebook
.ipynb_checkpoints

# Frontend
node_modules/
bower_components/
dist/
build/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
*.tsbuildinfo

# Java
*.class
*.jar
*.war
*.ear
*.iml
*.log
target/
out/
.gradle/
build/
.idea/
*.project
*.classpath
*.settings/
*.prefs

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes
*.swp

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# VSCode
.vscode/
.history/
.settings/