# 《逃出缅北》音频资源需求清单 - AI生成指南

## 1. 整体音频风格指导

### 1.1 核心音频风格
- **音乐类型：** 环境音乐 (Ambient) + 悬疑惊悚 (Thriller)
- **乐器选择：** 以电子合成器为主，配合少量真实乐器 (钢琴、弦乐)
- **音调特征：** 小调为主，营造压抑和不安的氛围
- **节奏特点：** 缓慢到中等节拍 (60-100 BPM)，紧张时刻可达120-140 BPM
- **动态范围：** 大动态对比，从极轻的环境音到突然的强音冲击

### 1.2 情绪氛围分类
- **压抑氛围：** 低频嗡鸣、缓慢的弦乐、稀疏的音符
- **紧张氛围：** 快速的心跳节奏、不协和音程、突然的音效
- **希望氛围：** 温暖的钢琴、上升的旋律线、明亮的和声
- **恐惧氛围：** 刺耳的高频、不规则节奏、回声效果

### 1.3 技术规格要求
- **音频格式：** WAV (无损) 用于制作，MP3 320kbps 用于游戏
- **采样率：** 44.1kHz 标准
- **声道：** 立体声 (Stereo)
- **音量标准：** -23 LUFS (符合游戏音频标准)
- **循环设计：** 所有背景音乐支持无缝循环

## 2. 背景音乐 (BGM) 需求

### 2.1 主菜单音乐
**音乐名称：** "困境序章" (Trapped Prelude)
**AI生成提示词：**
```
"Dark ambient menu music, slow tempo 70 BPM, minor key, synthesizer pads with low-frequency drones, sparse piano notes, building tension, mysterious atmosphere, 2-minute loop, cinematic style, suitable for game main menu"
```

**具体要求：**
- **时长：** 2分钟循环
- **情绪：** 神秘、不安、预示危险
- **乐器：** 合成器垫音、钢琴、低频嗡鸣
- **动态：** 渐强设计，从安静开始逐渐增强

### 2.2 第一章：办公室背景音乐
**音乐名称：** "虚假的日常" (False Routine)
**AI生成提示词：**
```
"Office ambient music with underlying tension, 80 BPM, repetitive electronic patterns suggesting monotonous work, subtle dissonance, fluorescent light humming sound, oppressive atmosphere, 3-minute seamless loop, industrial ambient style"
```

**分层音轨设计：**
- **基础层：** 持续的低频嗡鸣 (模拟日光灯声音)
- **节奏层：** 轻微的电子节拍 (模拟键盘敲击)
- **旋律层：** 稀疏的合成器旋律，带有不安感
- **氛围层：** 远处的人声嘈杂，营造拥挤感

### 2.3 第二章：宿舍背景音乐
**音乐名称：** "破碎的安息" (Broken Rest)
**AI生成提示词：**
```
"Melancholic dormitory ambient music, 65 BPM, soft piano with reverb, muffled sounds through walls, lonely atmosphere, minor key progression, gentle but sad, 4-minute loop, neo-classical ambient style"
```

**情绪变化设计：**
- **0-1分钟：** 安静的钢琴独奏，表现孤独
- **1-2分钟：** 加入轻柔的弦乐，增加温暖感
- **2-3分钟：** 引入轻微的不协和音，暗示不安
- **3-4分钟：** 回到开始的钢琴，形成循环

### 2.4 第三章：经理办公室音乐
**音乐名称：** "权力的阴影" (Shadow of Power)
**AI生成提示词：**
```
"Sinister office music, 90 BPM, dark orchestral elements, low brass, tense strings, electronic surveillance sounds, authoritarian atmosphere, dramatic tension, 3-minute loop, thriller soundtrack style"
```

**乐器编配：**
- **低音部：** 大提琴和低音提琴的持续音
- **中音部：** 法国号的威严旋律
- **高音部：** 小提琴的尖锐颤音
- **电子元素：** 监控设备的电子音效

### 2.5 第四章：逃离音乐
**音乐名称：** "黎明前的奔跑" (Run Before Dawn)
**AI生成提示词：**
```
"Intense escape music, 120 BPM building to 140 BPM, urgent percussion, rising orchestral elements, electronic beats, adrenaline-pumping rhythm, hope mixed with danger, 5-minute progressive composition, action thriller style"
```

**动态发展结构：**
- **第1分钟：** 轻柔开始，准备阶段的紧张
- **第2分钟：** 节奏加快，开始行动
- **第3分钟：** 高潮部分，最危险的时刻
- **第4分钟：** 短暂的缓解，接近成功
- **第5分钟：** 最终冲刺，胜利或失败的关键

### 2.6 结局音乐变奏

**完美结局：** "自由的曙光" (Dawn of Freedom)
```
"Triumphant ending music, 100 BPM, major key, uplifting orchestral arrangement, hope and relief, warm strings and brass, 2-minute composition, inspirational style"
```

**失败结局：** "绝望的深渊" (Abyss of Despair)
```
"Tragic ending music, 60 BPM, minor key, sorrowful strings, solo piano, deep regret and loss, 2-minute composition, melancholic classical style"
```

**污点结局：** "沉重的自由" (Heavy Freedom)
```
"Bittersweet ending music, 80 BPM, mixed major-minor tonality, conflicted emotions, piano and strings with dissonant elements, 2-minute composition, complex emotional style"
```

## 3. 音效 (SFX) 需求

### 3.1 环境音效

**办公室环境音效：**
- **日光灯嗡鸣：** 持续的50Hz低频嗡鸣，偶尔闪烁中断
- **键盘敲击：** 机械键盘的连续敲击声，节奏不规律
- **电话铃声：** 老式电话的刺耳铃声，3-5秒循环
- **空调运转：** 低频的机械运转声，偶有异响
- **脚步声：** 不同材质地面的脚步声 (瓷砖、地毯、木地板)

**AI生成提示词示例：**
```
"Fluorescent light buzzing sound, 50Hz frequency, electrical humming, office ambient noise, realistic foley sound effect, 10-second loop"
```

**宿舍环境音效：**
- **床铺吱嘎声：** 金属床架的摩擦声
- **水管声音：** 老旧水管的流水声和敲击声
- **门锁声音：** 金属锁具的开关声
- **窗外声音：** 远处的车辆声、人声嘈杂

### 3.2 交互音效

**成功操作音效：**
```
"Success notification sound, positive feedback, soft chime, 1-2 seconds duration, pleasant and reassuring, game UI sound effect"
```

**失败操作音效：**
```
"Error notification sound, negative feedback, low buzzer tone, 1 second duration, warning but not harsh, game UI sound effect"
```

**道具获得音效：**
```
"Item pickup sound, small object collection, soft click or clink, 0.5 seconds, satisfying feedback, inventory sound effect"
```

**危险警告音效：**
```
"Danger alert sound, tension building, low-frequency warning tone, 2-3 seconds, creates anxiety, thriller game sound effect"
```

### 3.3 特殊事件音效

**心跳声：**
```
"Heartbeat sound effect, realistic human heartbeat, variable tempo 60-120 BPM, stress response, medical accuracy, loopable"
```

**监控摄像头：**
```
"Security camera motor sound, mechanical whirring, electronic beep, surveillance equipment, 2-3 seconds, realistic foley"
```

**开锁音效：**
```
"Lock picking sound, metal tools on lock mechanism, realistic locksmith foley, 5-10 seconds, tension building"
```

**电子设备音效：**
```
"Computer startup sound, electronic beeps and whirs, retro technology, 3-5 seconds, nostalgic but functional"
```

## 4. 动态音频系统需求

### 4.1 自适应音乐系统
- **怀疑值响应：** 怀疑值越高，音乐越紧张
- **时间变化：** 白天和夜晚的音乐变化
- **场景转换：** 平滑的音乐过渡效果
- **情绪同步：** 根据剧情发展调整音乐强度

### 4.2 3D空间音效
- **距离衰减：** 声音随距离自然衰减
- **方向定位：** 左右声道的方向感
- **遮挡效果：** 墙壁等障碍物的声音遮挡
- **回声效果：** 不同空间的声学特性

### 4.3 交互式音频反馈
- **操作确认：** 每个交互都有对应音效
- **状态变化：** 系统状态改变的音频提示
- **进度指示：** 任务进度的音频反馈
- **错误提示：** 操作错误的音频警告

## 5. 语音和对话音频

### 5.1 角色语音需求
**主角内心独白：**
- **语音特征：** 年轻男性，普通话标准，略带紧张
- **情绪变化：** 从困惑到恐惧再到决心的变化
- **录制规格：** 44.1kHz, 16-bit, 单声道

**NPC对话语音：**
- **主管：** 中年男性，威严严厉的语调
- **室友：** 年轻男性，疲惫但友善的语调
- **其他角色：** 根据角色特征设计不同语音特色

### 5.2 AI语音生成指导
```
"Chinese male voice, age 22-25, nervous but determined tone, clear pronunciation, emotional range from fear to hope, suitable for game character dialogue"
```

## 6. 音频实现技术要求

### 6.1 循环和分层
- **无缝循环：** 所有背景音乐支持完美循环
- **分层混音：** 支持多层音轨的动态混合
- **淡入淡出：** 平滑的音频过渡效果
- **音量控制：** 独立的音乐、音效、语音音量控制

### 6.2 性能优化
- **文件压缩：** 在保证质量前提下最小化文件大小
- **流式加载：** 支持音频文件的流式播放
- **内存管理：** 及时释放不需要的音频资源
- **缓存策略：** 常用音效的预加载和缓存

### 6.3 平台适配
- **微信小游戏：** 符合微信小游戏的音频限制
- **移动设备：** 适配不同设备的音频性能
- **网络环境：** 支持低网速环境的音频播放
- **兼容性：** 确保在各种设备上的音频兼容性

---

**文档版本：** v1.0  
**最后更新：** 2025-07-19  
**负责人：** 音频总监  
**AI工具适配：** 支持Suno AI、AIVA、Mubert等主流AI音乐生成工具  
**语音AI工具：** 支持Azure Speech、Google Cloud TTS、科大讯飞等语音合成服务
