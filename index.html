<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>逃出缅北 - Escape from the North</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            overflow: hidden;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            touch-action: none;
        }
        
        #gameCanvas {
            display: block;
            margin: 0 auto;
            background: #1a1a1a;
            cursor: pointer;
        }
        
        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a237e, #424242);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
        }
        
        #loadingBar {
            width: 300px;
            height: 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-top: 20px;
        }
        
        #loadingProgress {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        #gameUI {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }
        
        .ui-element {
            pointer-events: auto;
        }
        
        #statusBar {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            height: 60px;
            background: rgba(0,0,0,0.7);
            border-radius: 10px;
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 14px;
        }
        
        #inventory {
            position: absolute;
            bottom: 80px;
            left: 10px;
            right: 10px;
            height: 80px;
            background: rgba(139, 69, 19, 0.8);
            border-radius: 10px;
            padding: 10px;
            display: flex;
            align-items: center;
            overflow-x: auto;
        }
        
        .inventory-slot {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            margin-right: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .inventory-slot:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
        }
        
        .inventory-slot img {
            max-width: 80%;
            max-height: 80%;
            object-fit: contain;
        }
        
        #controlButtons {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            height: 60px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .control-btn {
            width: 80px;
            height: 40px;
            background: rgba(0,0,0,0.7);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
        }
        
        #dialogBox {
            position: absolute;
            bottom: 200px;
            left: 20px;
            right: 20px;
            background: rgba(0,0,0,0.9);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-size: 16px;
            line-height: 1.5;
            display: none;
        }
        
        #dialogChoices {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .dialog-choice {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dialog-choice:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
        }
        
        .suspicion-meter {
            width: 100px;
            height: 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .suspicion-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #FFC107, #F44336);
            transition: width 0.5s ease;
        }
        
        .performance-meter {
            width: 100px;
            height: 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            overflow: hidden;
        }
        
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #F44336, #FFC107, #4CAF50);
            transition: width 0.5s ease;
        }
        
        /* 暂停覆盖层 */
        .pause-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .pause-content {
            background: rgba(40, 40, 40, 0.95);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #8B4513;
        }

        .pause-content h3 {
            color: #FFD700;
            margin-bottom: 20px;
        }

        .pause-content button {
            display: block;
            width: 200px;
            margin: 10px auto;
            padding: 10px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .pause-content button:hover {
            background: #A0522D;
        }

        /* 游戏菜单 */
        .game-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .menu-content {
            background: rgba(40, 40, 40, 0.95);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #8B4513;
        }

        .menu-content h3 {
            color: #FFD700;
            margin-bottom: 30px;
            font-size: 24px;
        }

        .menu-content button {
            display: block;
            width: 250px;
            margin: 15px auto;
            padding: 12px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .menu-content button:hover {
            background: #A0522D;
        }

        /* 设置面板 */
        .settings-panel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .settings-content {
            background: rgba(40, 40, 40, 0.95);
            padding: 30px;
            border-radius: 15px;
            border: 2px solid #8B4513;
            width: 400px;
            max-width: 90vw;
        }

        .settings-content h3 {
            color: #FFD700;
            text-align: center;
            margin-bottom: 25px;
            font-size: 20px;
        }

        .setting-group {
            margin-bottom: 20px;
            color: white;
        }

        .setting-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .setting-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .setting-group input[type="checkbox"] {
            margin-right: 10px;
        }

        .volume-value, .speed-value {
            float: right;
            color: #FFD700;
        }

        .settings-buttons {
            text-align: center;
            margin-top: 25px;
        }

        .settings-buttons button {
            margin: 0 10px;
            padding: 10px 20px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .settings-buttons button:hover {
            background: #A0522D;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 2000;
            animation: slideIn 0.3s ease-out;
        }

        .notification.info {
            background: #2196F3;
        }

        .notification.warning {
            background: #FF9800;
        }

        .notification.error {
            background: #F44336;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 结局界面 */
        .ending-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .ending-content {
            background: rgba(40, 40, 40, 0.95);
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #8B4513;
            max-width: 500px;
        }

        .ending-content h2 {
            color: #FFD700;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .ending-content p {
            color: white;
            font-size: 18px;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .ending-buttons button {
            margin: 0 10px;
            padding: 12px 24px;
            background: #8B4513;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }

        .ending-buttons button:hover {
            background: #A0522D;
        }

        @media (max-width: 768px) {
            #statusBar {
                font-size: 12px;
                height: 50px;
            }

            #inventory {
                height: 70px;
                bottom: 70px;
            }

            .inventory-slot {
                width: 50px;
                height: 50px;
            }

            .control-btn {
                width: 70px;
                height: 35px;
                font-size: 11px;
            }

            #dialogBox {
                font-size: 14px;
                bottom: 150px;
            }

            .settings-content {
                width: 350px;
                padding: 20px;
            }

            .pause-content, .menu-content {
                padding: 25px;
            }

            .ending-content {
                padding: 30px;
                max-width: 90vw;
            }
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loadingScreen">
        <h1 style="font-size: 2.5em; margin-bottom: 20px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">逃出缅北</h1>
        <p style="font-size: 1.2em; opacity: 0.8;">Escape from the North</p>
        <div id="loadingBar">
            <div id="loadingProgress"></div>
        </div>
        <p id="loadingText" style="margin-top: 15px; opacity: 0.7;">正在加载游戏资源...</p>
    </div>
    
    <!-- 游戏画布 -->
    <canvas id="gameCanvas"></canvas>
    
    <!-- 游戏UI -->
    <div id="gameUI">
        <!-- 状态栏 -->
        <div id="statusBar" class="ui-element">
            <div>
                <div>怀疑值</div>
                <div class="suspicion-meter">
                    <div class="suspicion-fill" style="width: 30%"></div>
                </div>
            </div>
            <div>
                <div>业绩值</div>
                <div class="performance-meter">
                    <div class="performance-fill" style="width: 0%"></div>
                </div>
            </div>
            <div id="timeDisplay">第1天 08:00</div>
        </div>
        
        <!-- 道具栏 -->
        <div id="inventory" class="ui-element">
            <div class="inventory-slot" data-slot="0"></div>
            <div class="inventory-slot" data-slot="1"></div>
            <div class="inventory-slot" data-slot="2"></div>
            <div class="inventory-slot" data-slot="3"></div>
            <div class="inventory-slot" data-slot="4"></div>
            <div class="inventory-slot" data-slot="5"></div>
            <div class="inventory-slot" data-slot="6"></div>
            <div class="inventory-slot" data-slot="7"></div>
        </div>
        
        <!-- 控制按钮 -->
        <div id="controlButtons" class="ui-element">
            <button class="control-btn" id="menuBtn">菜单</button>
            <button class="control-btn" id="inventoryBtn">道具</button>
            <button class="control-btn" id="settingsBtn">设置</button>
            <button class="control-btn" id="saveBtn">保存</button>
            <button class="control-btn" id="loadBtn">加载</button>
        </div>
        
        <!-- 对话框 -->
        <div id="dialogBox" class="ui-element">
            <div id="dialogText"></div>
            <div id="dialogChoices"></div>
        </div>
    </div>

    <!-- 暂停覆盖层 -->
    <div id="pauseOverlay" class="pause-overlay" style="display: none;">
        <div class="pause-content">
            <h3>游戏暂停</h3>
            <button onclick="game.resume()">继续游戏</button>
            <button onclick="app.showSettings()">设置</button>
            <button onclick="app.saveGame()">保存游戏</button>
            <button onclick="location.reload()">返回主菜单</button>
        </div>
    </div>

    <!-- 游戏菜单 -->
    <div id="gameMenu" class="game-menu" style="display: none;">
        <div class="menu-content">
            <h3>游戏菜单</h3>
            <button onclick="app.hideGameMenu()">继续游戏</button>
            <button onclick="app.saveGame()">保存游戏</button>
            <button onclick="app.loadGame()">加载游戏</button>
            <button onclick="app.showSettings()">设置</button>
            <button onclick="location.reload()">重新开始</button>
            <button onclick="location.reload()">退出游戏</button>
        </div>
    </div>

    <!-- 设置面板 -->
    <div id="settingsPanel" class="settings-panel" style="display: none;">
        <div class="settings-content">
            <h3>游戏设置</h3>

            <div class="setting-group">
                <label for="masterVolume">主音量</label>
                <input type="range" id="masterVolume" min="0" max="1" step="0.1" value="1">
                <span class="volume-value">100%</span>
            </div>

            <div class="setting-group">
                <label for="musicVolume">音乐音量</label>
                <input type="range" id="musicVolume" min="0" max="1" step="0.1" value="0.7">
                <span class="volume-value">70%</span>
            </div>

            <div class="setting-group">
                <label for="sfxVolume">音效音量</label>
                <input type="range" id="sfxVolume" min="0" max="1" step="0.1" value="0.8">
                <span class="volume-value">80%</span>
            </div>

            <div class="setting-group">
                <label for="textSpeed">文字速度</label>
                <input type="range" id="textSpeed" min="0.5" max="2" step="0.1" value="1">
                <span class="speed-value">正常</span>
            </div>

            <div class="setting-group">
                <label>
                    <input type="checkbox" id="autoSave" checked>
                    自动保存
                </label>
            </div>

            <div class="settings-buttons">
                <button onclick="app.applySettings()">应用</button>
                <button onclick="app.hideSettings()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/graphics.js"></script>
    <script src="js/input.js"></script>
    <script src="js/inventory.js"></script>
    <script src="js/dialog.js"></script>
    <script src="js/scenes.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
