<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console {
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>《逃出缅北》游戏测试页面</h1>
    
    <div class="test-section">
        <h2>系统检测</h2>
        <div id="systemStatus"></div>
        <button class="test-button" onclick="checkSystem()">检测系统兼容性</button>
    </div>
    
    <div class="test-section">
        <h2>资源加载测试</h2>
        <div id="resourceStatus"></div>
        <button class="test-button" onclick="testResources()">测试资源加载</button>
    </div>
    
    <div class="test-section">
        <h2>游戏系统测试</h2>
        <div id="gameStatus"></div>
        <button class="test-button" onclick="testGameSystems()">测试游戏系统</button>
    </div>
    
    <div class="test-section">
        <h2>控制台输出</h2>
        <div id="console"></div>
        <button class="test-button" onclick="clearConsole()">清空控制台</button>
    </div>
    
    <div class="test-section">
        <h2>启动游戏</h2>
        <p>如果所有测试都通过，可以启动完整游戏：</p>
        <button class="test-button" onclick="startGame()">启动游戏</button>
    </div>

    <script>
        // 重定向console.log到页面
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleDiv = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'warn' ? '#ff0' : '#0f0';
            consoleDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkSystem() {
            console.log('开始系统兼容性检测...');
            
            const checks = [
                {
                    name: 'HTML5 Canvas',
                    test: () => {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext && canvas.getContext('2d'));
                    }
                },
                {
                    name: 'Web Audio API',
                    test: () => {
                        return !!(window.AudioContext || window['webkitAudioContext']);
                    }
                },
                {
                    name: 'Local Storage',
                    test: () => {
                        try {
                            localStorage.setItem('test', 'test');
                            localStorage.removeItem('test');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }
                },
                {
                    name: 'Touch Events',
                    test: () => {
                        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                    }
                }
            ];
            
            let allPassed = true;
            let results = [];
            
            checks.forEach(check => {
                const passed = check.test();
                results.push(`${check.name}: ${passed ? '✓' : '✗'}`);
                if (!passed) allPassed = false;
                console.log(`${check.name}: ${passed ? 'PASS' : 'FAIL'}`);
            });
            
            showStatus('systemStatus', results.join('<br>'), allPassed ? 'success' : 'error');
        }
        
        function testResources() {
            console.log('开始资源加载测试...');
            
            // 测试图片加载
            const testImage = new Image();
            testImage.onload = () => {
                console.log('图片加载测试: PASS');
                showStatus('resourceStatus', '资源加载测试完成 - 将使用占位符资源', 'info');
            };
            testImage.onerror = () => {
                console.log('图片加载测试: FAIL (将使用占位符)');
                showStatus('resourceStatus', '资源文件不存在 - 将使用占位符资源', 'info');
            };
            testImage.src = 'assets/images/menu_bg.jpg';
            
            // 测试音频加载
            const testAudio = new Audio();
            testAudio.addEventListener('canplaythrough', () => {
                console.log('音频加载测试: PASS');
            });
            testAudio.addEventListener('error', () => {
                console.log('音频加载测试: FAIL (将使用静音模式)');
            });
            testAudio.src = 'assets/audio/menu_music.mp3';
        }
        
        function testGameSystems() {
            console.log('开始游戏系统测试...');
            
            try {
                // 测试工具函数
                if (typeof Utils !== 'undefined') {
                    console.log('Utils 类: PASS');
                } else {
                    console.log('Utils 类: FAIL');
                }
                
                // 测试事件系统
                const testEmitter = new EventEmitter();
                testEmitter.on('test', () => console.log('事件系统: PASS'));
                testEmitter.emit('test');
                
                showStatus('gameStatus', '游戏系统测试完成 - 准备就绪', 'success');
                
            } catch (error) {
                console.error('游戏系统测试失败:', error);
                showStatus('gameStatus', '游戏系统测试失败: ' + error.message, 'error');
            }
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = '';
        }
        
        function startGame() {
            console.log('启动完整游戏...');
            window.location.href = 'index.html';
        }
        
        // 页面加载完成后自动运行基本检测
        window.addEventListener('load', () => {
            console.log('测试页面加载完成');
            checkSystem();
        });
    </script>
    
    <!-- 加载游戏脚本进行测试 -->
    <script src="js/utils.js"></script>
</body>
</html>
