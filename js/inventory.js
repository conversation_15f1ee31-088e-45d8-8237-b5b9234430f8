// 道具系统
class InventoryManager extends EventEmitter {
    constructor() {
        super();
        this.items = new Map();
        this.inventory = [];
        this.maxSlots = 8;
        this.selectedItem = null;
        this.draggedItem = null;
        
        // UI元素
        this.inventoryElement = document.getElementById('inventory');
        this.slots = [];
        
        this.initializeUI();
        this.setupEventListeners();
    }
    
    // 初始化UI
    initializeUI() {
        this.slots = Array.from(this.inventoryElement.querySelectorAll('.inventory-slot'));
        this.updateUI();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        this.slots.forEach((slot, index) => {
            slot.addEventListener('click', (e) => this.handleSlotClick(index, e));
            slot.addEventListener('dragstart', (e) => this.handleDragStart(index, e));
            slot.addEventListener('dragover', (e) => this.handleDragOver(e));
            slot.addEventListener('drop', (e) => this.handleDrop(index, e));
            slot.addEventListener('dragend', (e) => this.handleDragEnd(e));
        });
    }
    
    // 定义道具类型
    defineItem(id, data) {
        this.items.set(id, {
            id,
            name: data.name || id,
            description: data.description || '',
            icon: data.icon || null,
            stackable: data.stackable || false,
            maxStack: data.maxStack || 1,
            usable: data.usable || false,
            combinable: data.combinable || false,
            combinesWith: data.combinesWith || [],
            onUse: data.onUse || null,
            onCombine: data.onCombine || null,
            category: data.category || 'misc',
            rarity: data.rarity || 'common'
        });
        
        this.emit('itemDefined', { id, data });
    }
    
    // 添加道具到背包
    addItem(itemId, quantity = 1) {
        const itemData = this.items.get(itemId);
        if (!itemData) {
            console.warn(`Item ${itemId} not defined`);
            return false;
        }
        
        // 检查是否可以堆叠
        if (itemData.stackable) {
            const existingSlot = this.inventory.findIndex(slot => 
                slot && slot.id === itemId && slot.quantity < itemData.maxStack
            );
            
            if (existingSlot !== -1) {
                const availableSpace = itemData.maxStack - this.inventory[existingSlot].quantity;
                const addAmount = Math.min(quantity, availableSpace);
                
                this.inventory[existingSlot].quantity += addAmount;
                quantity -= addAmount;
                
                this.emit('itemAdded', { 
                    itemId, 
                    quantity: addAmount, 
                    slot: existingSlot,
                    stacked: true 
                });
            }
        }
        
        // 添加到新槽位
        while (quantity > 0 && this.inventory.length < this.maxSlots) {
            const emptySlot = this.inventory.findIndex(slot => slot === null);
            const slotIndex = emptySlot !== -1 ? emptySlot : this.inventory.length;
            
            if (slotIndex >= this.maxSlots) break;
            
            const addAmount = Math.min(quantity, itemData.stackable ? itemData.maxStack : 1);
            
            const inventoryItem = {
                id: itemId,
                quantity: addAmount,
                data: itemData
            };
            
            if (emptySlot !== -1) {
                this.inventory[emptySlot] = inventoryItem;
            } else {
                this.inventory.push(inventoryItem);
            }
            
            quantity -= addAmount;
            
            this.emit('itemAdded', { 
                itemId, 
                quantity: addAmount, 
                slot: slotIndex,
                stacked: false 
            });
        }
        
        this.updateUI();
        return quantity === 0; // 返回是否完全添加成功
    }
    
    // 移除道具
    removeItem(itemId, quantity = 1) {
        let remainingToRemove = quantity;
        
        for (let i = this.inventory.length - 1; i >= 0 && remainingToRemove > 0; i--) {
            const slot = this.inventory[i];
            if (slot && slot.id === itemId) {
                const removeAmount = Math.min(remainingToRemove, slot.quantity);
                slot.quantity -= removeAmount;
                remainingToRemove -= removeAmount;
                
                if (slot.quantity <= 0) {
                    this.inventory[i] = null;
                }
                
                this.emit('itemRemoved', { 
                    itemId, 
                    quantity: removeAmount, 
                    slot: i 
                });
            }
        }
        
        // 压缩背包（移除空槽位）
        this.compactInventory();
        this.updateUI();
        
        return remainingToRemove === 0;
    }
    
    // 使用道具
    useItem(slotIndex) {
        const slot = this.inventory[slotIndex];
        if (!slot) return false;
        
        const itemData = slot.data;
        if (!itemData.usable) {
            this.emit('itemNotUsable', { itemId: slot.id });
            return false;
        }
        
        // 执行使用效果
        let consumed = false;
        if (itemData.onUse) {
            consumed = itemData.onUse(slot, slotIndex);
        }
        
        // 如果道具被消耗，减少数量
        if (consumed) {
            slot.quantity--;
            if (slot.quantity <= 0) {
                this.inventory[slotIndex] = null;
            }
        }
        
        this.emit('itemUsed', { 
            itemId: slot.id, 
            slot: slotIndex, 
            consumed 
        });
        
        this.updateUI();
        return true;
    }
    
    // 组合道具
    combineItems(slot1Index, slot2Index) {
        const slot1 = this.inventory[slot1Index];
        const slot2 = this.inventory[slot2Index];
        
        if (!slot1 || !slot2) return false;
        
        const item1Data = slot1.data;
        const item2Data = slot2.data;
        
        // 检查是否可以组合
        if (!item1Data.combinable || !item2Data.combinable) {
            this.emit('itemsNotCombinable', { 
                item1: slot1.id, 
                item2: slot2.id 
            });
            return false;
        }
        
        // 检查组合规则
        const canCombine = item1Data.combinesWith.includes(slot2.id) || 
                          item2Data.combinesWith.includes(slot1.id);
        
        if (!canCombine) {
            this.emit('invalidCombination', { 
                item1: slot1.id, 
                item2: slot2.id 
            });
            return false;
        }
        
        // 执行组合
        let result = null;
        if (item1Data.onCombine) {
            result = item1Data.onCombine(slot1, slot2);
        } else if (item2Data.onCombine) {
            result = item2Data.onCombine(slot2, slot1);
        }
        
        if (result) {
            // 移除原道具
            this.inventory[slot1Index] = null;
            this.inventory[slot2Index] = null;
            
            // 添加新道具
            this.addItem(result.id, result.quantity || 1);
            
            this.emit('itemsCombined', { 
                item1: slot1.id, 
                item2: slot2.id, 
                result: result.id 
            });
            
            this.updateUI();
            return true;
        }
        
        return false;
    }
    
    // 检查是否有道具
    hasItem(itemId, quantity = 1) {
        let totalQuantity = 0;
        for (const slot of this.inventory) {
            if (slot && slot.id === itemId) {
                totalQuantity += slot.quantity;
            }
        }
        return totalQuantity >= quantity;
    }
    
    // 获取道具数量
    getItemQuantity(itemId) {
        let totalQuantity = 0;
        for (const slot of this.inventory) {
            if (slot && slot.id === itemId) {
                totalQuantity += slot.quantity;
            }
        }
        return totalQuantity;
    }
    
    // 压缩背包
    compactInventory() {
        this.inventory = this.inventory.filter(slot => slot !== null);
        while (this.inventory.length < this.maxSlots) {
            this.inventory.push(null);
        }
    }
    
    // 更新UI
    updateUI() {
        this.slots.forEach((slot, index) => {
            const item = this.inventory[index];
            
            // 清空槽位
            slot.innerHTML = '';
            slot.className = 'inventory-slot';
            
            if (item) {
                // 添加道具图标
                if (item.data.icon) {
                    const img = document.createElement('img');
                    img.src = item.data.icon;
                    img.alt = item.data.name;
                    slot.appendChild(img);
                }
                
                // 添加数量显示
                if (item.quantity > 1) {
                    const quantitySpan = document.createElement('span');
                    quantitySpan.textContent = item.quantity;
                    quantitySpan.style.position = 'absolute';
                    quantitySpan.style.bottom = '2px';
                    quantitySpan.style.right = '2px';
                    quantitySpan.style.color = 'white';
                    quantitySpan.style.fontSize = '12px';
                    quantitySpan.style.fontWeight = 'bold';
                    quantitySpan.style.textShadow = '1px 1px 1px black';
                    slot.appendChild(quantitySpan);
                }
                
                // 添加稀有度样式
                slot.classList.add(`rarity-${item.data.rarity}`);
                
                // 设置拖拽属性
                slot.draggable = true;
                
                // 设置工具提示
                slot.title = `${item.data.name}\n${item.data.description}`;
            } else {
                slot.draggable = false;
                slot.title = '';
            }
            
            // 选中状态
            if (this.selectedItem === index) {
                slot.classList.add('selected');
            }
        });
    }
    
    // 槽位点击处理
    handleSlotClick(index, event) {
        event.preventDefault();
        
        const item = this.inventory[index];
        if (!item) return;
        
        if (this.selectedItem === index) {
            // 取消选择或使用道具
            if (event.detail === 2) { // 双击
                this.useItem(index);
            }
            this.selectedItem = null;
        } else if (this.selectedItem !== null) {
            // 尝试组合道具
            this.combineItems(this.selectedItem, index);
            this.selectedItem = null;
        } else {
            // 选择道具
            this.selectedItem = index;
        }
        
        this.updateUI();
        this.emit('slotClicked', { index, item });
    }
    
    // 拖拽处理
    handleDragStart(index, event) {
        this.draggedItem = index;
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', index.toString());
    }
    
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
    }
    
    handleDrop(index, event) {
        event.preventDefault();
        const sourceIndex = parseInt(event.dataTransfer.getData('text/plain'));
        
        if (sourceIndex !== index) {
            // 交换道具位置
            const temp = this.inventory[sourceIndex];
            this.inventory[sourceIndex] = this.inventory[index];
            this.inventory[index] = temp;
            
            this.updateUI();
            this.emit('itemsMoved', { from: sourceIndex, to: index });
        }
    }
    
    handleDragEnd(event) {
        this.draggedItem = null;
    }
    
    // 保存/加载背包状态
    saveState() {
        const state = {
            inventory: this.inventory.map(slot => slot ? {
                id: slot.id,
                quantity: slot.quantity
            } : null)
        };
        return state;
    }
    
    loadState(state) {
        this.inventory = [];
        
        if (state.inventory) {
            state.inventory.forEach(slotData => {
                if (slotData) {
                    const itemData = this.items.get(slotData.id);
                    if (itemData) {
                        this.inventory.push({
                            id: slotData.id,
                            quantity: slotData.quantity,
                            data: itemData
                        });
                    } else {
                        this.inventory.push(null);
                    }
                } else {
                    this.inventory.push(null);
                }
            });
        }
        
        // 确保背包有足够的槽位
        while (this.inventory.length < this.maxSlots) {
            this.inventory.push(null);
        }
        
        this.updateUI();
        this.emit('stateLoaded');
    }
    
    // 清空背包
    clear() {
        this.inventory = new Array(this.maxSlots).fill(null);
        this.selectedItem = null;
        this.updateUI();
        this.emit('inventoryCleared');
    }
}

// 创建全局道具管理器实例
window.inventoryManager = new InventoryManager();
