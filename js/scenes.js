// 场景管理系统
class Scene extends EventEmitter {
    constructor(id, data) {
        super();
        this.id = id;
        this.name = data.name || id;
        this.background = data.background || null;
        this.music = data.music || null;
        this.ambientSound = data.ambientSound || null;
        
        // 交互区域
        this.interactables = data.interactables || [];
        this.hotspots = data.hotspots || [];
        
        // 场景状态
        this.isLoaded = false;
        this.isActive = false;
        this.visitCount = 0;
        this.flags = new Map();
        
        // 动画和效果
        this.animations = [];
        this.particles = [];
        
        // 事件处理
        this.onEnter = data.onEnter || null;
        this.onExit = data.onExit || null;
        this.onUpdate = data.onUpdate || null;
        this.onRender = data.onRender || null;
    }
    
    // 进入场景
    async enter() {
        this.isActive = true;
        this.visitCount++;
        
        // 播放背景音乐
        if (this.music) {
            audioManager.playMusic(this.music, true, 1000);
        }
        
        // 播放环境音效
        if (this.ambientSound) {
            audioManager.playSound(this.ambientSound, 0.3, true);
        }
        
        // 执行进入回调
        if (this.onEnter) {
            await this.onEnter();
        }
        
        this.emit('entered');
    }
    
    // 离开场景
    async exit() {
        this.isActive = false;
        
        // 执行离开回调
        if (this.onExit) {
            await this.onExit();
        }
        
        this.emit('exited');
    }
    
    // 更新场景
    update(deltaTime) {
        if (!this.isActive) return;
        
        // 更新动画
        this.animations = this.animations.filter(anim => {
            anim.update(deltaTime);
            return anim.isActive;
        });
        
        // 更新粒子
        this.particles = this.particles.filter(particle => {
            particle.update(deltaTime);
            return particle.isAlive;
        });
        
        // 执行更新回调
        if (this.onUpdate) {
            this.onUpdate(deltaTime);
        }
    }
    
    // 渲染场景
    render(graphics) {
        if (!this.isActive) return;
        
        // 渲染背景
        if (this.background) {
            graphics.drawImage(this.background, 0, 0, graphics.canvas.width, graphics.canvas.height);
        }
        
        // 渲染交互区域（调试模式）
        if (game.debugMode) {
            this.renderDebugInfo(graphics);
        }
        
        // 渲染动画
        this.animations.forEach(anim => anim.render(graphics));
        
        // 渲染粒子
        this.particles.forEach(particle => particle.render(graphics));
        
        // 执行渲染回调
        if (this.onRender) {
            this.onRender(graphics);
        }
    }
    
    // 渲染调试信息
    renderDebugInfo(graphics) {
        // 渲染交互区域
        this.interactables.forEach(item => {
            if (item.bounds) {
                graphics.drawRect(
                    item.bounds.x, item.bounds.y,
                    item.bounds.width, item.bounds.height,
                    'rgba(0, 255, 0, 0.3)', true
                );
                graphics.drawRect(
                    item.bounds.x, item.bounds.y,
                    item.bounds.width, item.bounds.height,
                    '#00ff00', false
                );
            }
        });
        
        // 渲染热点区域
        this.hotspots.forEach(hotspot => {
            if (hotspot.bounds) {
                graphics.drawRect(
                    hotspot.bounds.x, hotspot.bounds.y,
                    hotspot.bounds.width, hotspot.bounds.height,
                    'rgba(255, 0, 0, 0.3)', true
                );
                graphics.drawRect(
                    hotspot.bounds.x, hotspot.bounds.y,
                    hotspot.bounds.width, hotspot.bounds.height,
                    '#ff0000', false
                );
            }
        });
    }
    
    // 处理点击事件
    handleClick(x, y) {
        // 检查交互物品
        for (const item of this.interactables) {
            if (this.isPointInBounds(x, y, item.bounds)) {
                if (item.condition && !item.condition()) {
                    continue; // 不满足条件
                }
                
                this.interactWithItem(item);
                return true;
            }
        }
        
        // 检查热点区域
        for (const hotspot of this.hotspots) {
            if (this.isPointInBounds(x, y, hotspot.bounds)) {
                if (hotspot.condition && !hotspot.condition()) {
                    continue;
                }
                
                this.triggerHotspot(hotspot);
                return true;
            }
        }
        
        return false;
    }
    
    // 检查点是否在边界内
    isPointInBounds(x, y, bounds) {
        if (!bounds) return false;
        
        if (bounds.type === 'rect' || !bounds.type) {
            return Utils.isPointInRect(x, y, bounds);
        } else if (bounds.type === 'circle') {
            return Utils.isPointInCircle(x, y, bounds);
        }
        
        return false;
    }
    
    // 与物品交互
    interactWithItem(item) {
        // 播放交互音效
        if (item.sound) {
            audioManager.playSound(item.sound);
        }
        
        // 显示高亮效果
        if (item.bounds && window.graphics) {
            window.graphics.drawHighlight(
                item.bounds.x, item.bounds.y,
                item.bounds.width, item.bounds.height
            );
        }
        
        // 执行交互动作
        if (item.action) {
            item.action();
        }
        
        // 显示对话
        if (item.dialog) {
            dialogManager.show(item.dialog);
        }
        
        // 添加道具
        if (item.giveItem) {
            inventoryManager.addItem(item.giveItem.id, item.giveItem.quantity || 1);
            audioManager.playSound('success');
        }
        
        // 设置标志
        if (item.setFlag) {
            Object.keys(item.setFlag).forEach(flag => {
                this.setFlag(flag, item.setFlag[flag]);
            });
        }
        
        // 标记为已使用
        if (item.oneTime) {
            item.used = true;
        }
        
        this.emit('itemInteracted', item);
    }
    
    // 触发热点
    triggerHotspot(hotspot) {
        if (hotspot.action) {
            hotspot.action();
        }
        
        if (hotspot.changeScene) {
            game.changeScene(hotspot.changeScene);
        }
        
        if (hotspot.dialog) {
            dialogManager.show(hotspot.dialog);
        }
        
        this.emit('hotspotTriggered', hotspot);
    }
    
    // 设置标志
    setFlag(flag, value) {
        this.flags.set(flag, value);
        this.emit('flagSet', { flag, value });
    }
    
    // 获取标志
    getFlag(flag, defaultValue = false) {
        return this.flags.get(flag) || defaultValue;
    }
    
    // 添加交互物品
    addInteractable(item) {
        this.interactables.push(item);
    }
    
    // 移除交互物品
    removeInteractable(itemId) {
        this.interactables = this.interactables.filter(item => item.id !== itemId);
    }
    
    // 添加热点
    addHotspot(hotspot) {
        this.hotspots.push(hotspot);
    }
    
    // 移除热点
    removeHotspot(hotspotId) {
        this.hotspots = this.hotspots.filter(hotspot => hotspot.id !== hotspotId);
    }
    
    // 保存场景状态
    saveState() {
        return {
            id: this.id,
            visitCount: this.visitCount,
            flags: Array.from(this.flags.entries()),
            interactables: this.interactables.map(item => ({
                id: item.id,
                used: item.used || false
            }))
        };
    }
    
    // 加载场景状态
    loadState(state) {
        this.visitCount = state.visitCount || 0;

        if (state.flags) {
            this.flags = new Map(state.flags);
        }

        if (state.interactables) {
            state.interactables.forEach(savedItem => {
                const item = this.interactables.find(i => i.id === savedItem.id);
                if (item) {
                    item.used = savedItem.used;
                }
            });
        }
    }
}

// 场景管理器
class SceneManager extends EventEmitter {
    constructor() {
        super();
        this.scenes = new Map();
        this.currentScene = null;
        this.previousScene = null;
        this.isTransitioning = false;
        this.transitionDuration = 1000;
    }

    // 注册场景
    registerScene(scene) {
        this.scenes.set(scene.id, scene);
        this.emit('sceneRegistered', scene);
    }

    // 获取场景
    getScene(id) {
        return this.scenes.get(id);
    }

    // 切换场景
    async changeScene(sceneId, transition = 'fade') {
        if (this.isTransitioning) return false;

        const newScene = this.scenes.get(sceneId);
        if (!newScene) {
            console.error(`Scene ${sceneId} not found`);
            return false;
        }

        this.isTransitioning = true;
        this.emit('transitionStart', { from: this.currentScene?.id, to: sceneId });

        // 执行转场效果
        await this.performTransition(transition);

        // 离开当前场景
        if (this.currentScene) {
            await this.currentScene.exit();
            this.previousScene = this.currentScene;
        }

        // 进入新场景
        this.currentScene = newScene;
        await this.currentScene.enter();

        this.isTransitioning = false;
        this.emit('sceneChanged', { from: this.previousScene?.id, to: sceneId });

        return true;
    }

    // 执行转场效果
    async performTransition(type) {
        return new Promise(resolve => {
            switch (type) {
                case 'fade':
                    graphics.fadeToColor('#000000', this.transitionDuration / 2000, () => {
                        graphics.fadeFromColor(this.transitionDuration / 2000, resolve);
                    });
                    break;

                case 'flash':
                    graphics.flash('#ffffff', this.transitionDuration / 1000);
                    setTimeout(resolve, this.transitionDuration);
                    break;

                case 'shake':
                    graphics.shake(10, this.transitionDuration / 1000);
                    setTimeout(resolve, this.transitionDuration);
                    break;

                default:
                    setTimeout(resolve, this.transitionDuration);
            }
        });
    }

    // 更新当前场景
    update(deltaTime) {
        if (this.currentScene) {
            this.currentScene.update(deltaTime);
        }
    }

    // 渲染当前场景
    render(graphics) {
        if (this.currentScene) {
            this.currentScene.render(graphics);
        }
    }

    // 处理输入
    handleInput(inputType, data) {
        if (this.currentScene && !this.isTransitioning) {
            if (inputType === 'click' || inputType === 'tap') {
                return this.currentScene.handleClick(data.x, data.y);
            }
        }
        return false;
    }

    // 保存所有场景状态
    saveState() {
        const sceneStates = {};
        this.scenes.forEach((scene, id) => {
            sceneStates[id] = scene.saveState();
        });

        return {
            currentScene: this.currentScene?.id,
            previousScene: this.previousScene?.id,
            sceneStates
        };
    }

    // 加载所有场景状态
    loadState(state) {
        if (state.sceneStates) {
            Object.keys(state.sceneStates).forEach(sceneId => {
                const scene = this.scenes.get(sceneId);
                if (scene) {
                    scene.loadState(state.sceneStates[sceneId]);
                }
            });
        }

        if (state.currentScene) {
            this.changeScene(state.currentScene, 'none');
        }
    }
}

// 创建具体场景
function createGameScenes() {
    const scenes = [];

    // 主菜单场景
    scenes.push(new Scene('menu', {
        name: '主菜单',
        background: 'menu_bg',
        music: 'menu_music',

        interactables: [
            {
                id: 'start_button',
                bounds: { x: 260, y: 400, width: 200, height: 60 },
                action: () => sceneManager.changeScene('office'),
                sound: 'success'
            },
            {
                id: 'settings_button',
                bounds: { x: 260, y: 480, width: 200, height: 60 },
                action: () => game.showSettings(),
                sound: 'success'
            }
        ]
    }));

    // 办公室场景
    scenes.push(new Scene('office', {
        name: '办公室',
        background: 'office_bg',
        music: 'office_music',
        ambientSound: 'fluorescent_hum',

        onEnter: async function() {
            // 第一次进入办公室的特殊事件
            if (this.visitCount === 1) {
                setTimeout(() => {
                    dialogManager.show(dialogManager.getDialogTemplates().firstScamTask);
                }, 2000);
            }
        },

        interactables: [
            {
                id: 'computer',
                bounds: { x: 300, y: 200, width: 120, height: 80 },
                dialog: {
                    text: '这是你的工作电脑。屏幕上显示着诈骗话术和目标客户信息。',
                    choices: [
                        {
                            text: '开始工作',
                            action: () => game.startScamTask(),
                            effects: { performance: 1 }
                        },
                        {
                            text: '假装工作',
                            action: () => game.fakeWork(),
                            effects: { suspicion: 2 }
                        },
                        {
                            text: '离开',
                            action: () => {}
                        }
                    ]
                },
                sound: 'success'
            },
            {
                id: 'supervisor_desk',
                bounds: { x: 100, y: 150, width: 100, height: 60 },
                condition: () => game.getFlag('supervisor_available'),
                dialog: {
                    speaker: '主管',
                    text: '有什么事吗？记住，业绩就是一切！',
                    choices: [
                        {
                            text: '汇报工作进度',
                            action: () => game.reportProgress()
                        },
                        {
                            text: '请假',
                            effects: { suspicion: 5 },
                            next: () => dialogManager.getDialogTemplates().supervisorImpatient
                        },
                        {
                            text: '没事，我继续工作',
                            effects: { suspicion: -1 }
                        }
                    ]
                }
            },
            {
                id: 'trash_can',
                bounds: { x: 50, y: 300, width: 40, height: 50 },
                action: () => {
                    if (inventoryManager.hasItem('evidence')) {
                        inventoryManager.removeItem('evidence');
                        game.setFlag('evidence_destroyed', true);
                        audioManager.playSound('success');
                    }
                }
            }
        ],

        hotspots: [
            {
                id: 'exit_to_dormitory',
                bounds: { x: 650, y: 200, width: 70, height: 200 },
                condition: () => game.canLeaveDuringWork(),
                changeScene: 'dormitory'
            }
        ]
    }));

    // 宿舍场景
    scenes.push(new Scene('dormitory', {
        name: '宿舍',
        background: 'dormitory_bg',
        music: 'dormitory_music',

        onEnter: async function() {
            // 第一次进入宿舍时遇到室友
            if (this.visitCount === 1) {
                setTimeout(() => {
                    dialogManager.show(dialogManager.getDialogTemplates().roommateHelp);
                }, 1000);
            }
        },

        interactables: [
            {
                id: 'bed',
                bounds: { x: 100, y: 250, width: 150, height: 80 },
                dialog: {
                    text: '这是你的床铺。你想做什么？',
                    choices: [
                        {
                            text: '休息一下',
                            action: () => {
                                game.changeSuspicion(-3);
                                game.advanceTime(2);
                                audioManager.playSound('success');
                            }
                        },
                        {
                            text: '搜查床铺',
                            condition: () => !game.getFlag('bed_searched'),
                            action: () => {
                                game.setFlag('bed_searched', true);
                                inventoryManager.addItem('money', 50);
                                audioManager.playSound('success');
                            }
                        },
                        {
                            text: '离开',
                            action: () => {}
                        }
                    ]
                }
            },
            {
                id: 'roommate',
                bounds: { x: 400, y: 200, width: 80, height: 120 },
                condition: () => game.hour >= 20 || game.hour <= 6,
                dialog: {
                    speaker: '室友小王',
                    text: '兄弟，有什么需要帮助的吗？',
                    choices: [
                        {
                            text: '询问逃跑路线',
                            condition: () => game.getFlag('roommate_trust') >= 2,
                            action: () => {
                                inventoryManager.addItem('escape_map');
                                game.setFlag('knows_escape_route', true);
                            }
                        },
                        {
                            text: '借点钱',
                            condition: () => game.getFlag('roommate_trust') >= 1,
                            action: () => {
                                inventoryManager.addItem('money', 100);
                                game.setFlag('owes_roommate', true);
                            }
                        },
                        {
                            text: '没什么',
                            action: () => {}
                        }
                    ]
                }
            },
            {
                id: 'window',
                bounds: { x: 600, y: 100, width: 100, height: 150 },
                dialog: {
                    text: '窗户外面是三楼的高度，下面是园区的围墙。',
                    choices: [
                        {
                            text: '观察外面的情况',
                            action: () => game.observeOutside()
                        },
                        {
                            text: '尝试打开窗户',
                            condition: () => inventoryManager.hasItem('screwdriver'),
                            action: () => game.openWindow()
                        },
                        {
                            text: '离开',
                            action: () => {}
                        }
                    ]
                }
            }
        ],

        hotspots: [
            {
                id: 'exit_to_office',
                bounds: { x: 0, y: 200, width: 50, height: 200 },
                changeScene: 'office'
            },
            {
                id: 'exit_to_corridor',
                bounds: { x: 350, y: 450, width: 100, height: 50 },
                changeScene: 'corridor'
            }
        ]
    }));

    // 走廊场景
    scenes.push(new Scene('corridor', {
        name: '走廊',
        background: 'corridor_bg',
        music: 'office_music',
        ambientSound: 'fluorescent_hum',

        interactables: [
            {
                id: 'security_camera',
                bounds: { x: 350, y: 50, width: 60, height: 40 },
                dialog: {
                    text: '这是一个监控摄像头，正在录制。',
                    choices: [
                        {
                            text: '尝试破坏摄像头',
                            condition: () => inventoryManager.hasItem('screwdriver'),
                            action: () => {
                                game.setFlag('camera_disabled', true);
                                game.changeSuspicion(10);
                                audioManager.playSound('error');
                            }
                        },
                        {
                            text: '避开摄像头',
                            action: () => game.changeSuspicion(-1)
                        }
                    ]
                }
            },
            {
                id: 'guard_room',
                bounds: { x: 500, y: 200, width: 100, height: 80 },
                condition: () => game.hour >= 2 && game.hour <= 5,
                dialog: {
                    text: '保安室的门是开着的，里面的保安在打瞌睡。',
                    choices: [
                        {
                            text: '偷偷进入',
                            action: () => sceneManager.changeScene('guard_room')
                        },
                        {
                            text: '不要冒险',
                            action: () => {}
                        }
                    ]
                }
            }
        ],

        hotspots: [
            {
                id: 'back_to_dormitory',
                bounds: { x: 100, y: 450, width: 100, height: 50 },
                changeScene: 'dormitory'
            },
            {
                id: 'to_exit',
                bounds: { x: 650, y: 200, width: 70, height: 200 },
                condition: () => game.getFlag('has_exit_key') || game.suspicion < 20,
                changeScene: 'exit_area'
            }
        ]
    }));

    // 出口区域场景
    scenes.push(new Scene('exit_area', {
        name: '出口区域',
        background: 'exit_bg',
        music: 'escape_music',

        onEnter: async function() {
            // 进入出口区域时的紧张音效
            audioManager.playSound('heartbeat', 0.3, true);
        },

        interactables: [
            {
                id: 'main_gate',
                bounds: { x: 300, y: 150, width: 120, height: 200 },
                dialog: {
                    text: '这是园区的主门，有保安把守。',
                    choices: [
                        {
                            text: '直接冲出去',
                            action: () => game.attemptEscape('force')
                        },
                        {
                            text: '等待机会',
                            action: () => game.waitForOpportunity()
                        },
                        {
                            text: '寻找其他出路',
                            action: () => game.findAlternativeExit()
                        }
                    ]
                }
            },
            {
                id: 'guard_post',
                bounds: { x: 150, y: 200, width: 80, height: 100 },
                condition: () => !game.getFlag('guard_distracted'),
                dialog: {
                    text: '保安正在值班，看起来很警觉。',
                    choices: [
                        {
                            text: '制造干扰',
                            condition: () => inventoryManager.hasItem('stone'),
                            action: () => game.distractGuard()
                        },
                        {
                            text: '贿赂保安',
                            condition: () => inventoryManager.hasItem('money') && inventoryManager.getItemQuantity('money') >= 500,
                            action: () => game.bribeGuard()
                        },
                        {
                            text: '离开',
                            action: () => {}
                        }
                    ]
                }
            }
        ]
    }));

    return scenes;
}

// 创建全局场景管理器实例
window.sceneManager = new SceneManager();
