// 主程序入口
class GameApplication {
    constructor() {
        this.canvas = null;
        this.graphics = null;
        this.input = null;
        this.isInitialized = false;
        this.loadingProgress = 0;
        this.loadingSteps = [
            '初始化画布...',
            '加载音频资源...',
            '加载图片资源...',
            '初始化游戏系统...',
            '准备场景...',
            '完成初始化'
        ];
        this.currentStep = 0;
    }
    
    // 应用程序初始化
    async init() {
        try {
            console.log('开始初始化游戏应用...');
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 初始化画布和图形系统
            await this.initCanvas();
            this.updateLoadingProgress();
            
            // 初始化输入系统
            this.initInput();
            
            // 初始化游戏核心
            await this.initGame();
            this.updateLoadingProgress();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 完成初始化
            this.isInitialized = true;
            this.updateLoadingProgress();
            
            console.log('游戏应用初始化完成');
            
            // 隐藏加载界面，显示游戏
            setTimeout(() => {
                this.hideLoadingScreen();
                this.startGame();
            }, 1000);
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            this.showError('游戏初始化失败: ' + error.message);
        }
    }
    
    // 初始化画布
    async initCanvas() {
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('找不到游戏画布元素');
        }
        
        // 创建图形管理器
        this.graphics = new GraphicsManager(this.canvas);
        window.graphics = this.graphics;
        
        // 设置画布大小
        this.resizeCanvas();
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.resizeCanvas();
        });
        
        console.log('画布初始化完成');
    }
    
    // 调整画布大小
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // 设置画布显示大小
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // 设置画布实际大小（考虑设备像素比）
        const pixelRatio = window.devicePixelRatio || 1;
        this.canvas.width = rect.width * pixelRatio;
        this.canvas.height = rect.height * pixelRatio;
        
        // 调整图形管理器
        if (this.graphics) {
            this.graphics.resize();
        }
    }
    
    // 初始化输入系统
    initInput() {
        this.input = new InputManager(this.canvas);
        window.input = this.input;
        
        // 设置输入事件处理
        this.input.on('click', (data) => {
            if (this.isInitialized && game.isRunning) {
                sceneManager.handleInput('click', data);
            }
        });
        
        this.input.on('tap', (data) => {
            if (this.isInitialized && game.isRunning) {
                sceneManager.handleInput('tap', data);
            }
        });
        
        // 处理UI按钮点击
        this.setupUIEventListeners();
        
        console.log('输入系统初始化完成');
    }
    
    // 设置UI事件监听器
    setupUIEventListeners() {
        // 菜单按钮
        const menuBtn = document.getElementById('menuBtn');
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                this.showGameMenu();
            });
        }
        
        // 设置按钮
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettings();
            });
        }
        
        // 道具栏按钮
        const inventoryBtn = document.getElementById('inventoryBtn');
        if (inventoryBtn) {
            inventoryBtn.addEventListener('click', () => {
                this.toggleInventory();
            });
        }
        
        // 保存按钮
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveGame();
            });
        }
        
        // 加载按钮
        const loadBtn = document.getElementById('loadBtn');
        if (loadBtn) {
            loadBtn.addEventListener('click', () => {
                this.loadGame();
            });
        }
    }
    
    // 初始化游戏
    async initGame() {
        // 初始化游戏核心
        const success = await game.init();
        if (!success) {
            throw new Error('游戏核心初始化失败');
        }
        
        console.log('游戏核心初始化完成');
    }
    
    // 设置全局事件监听器
    setupEventListeners() {
        // 游戏事件
        game.on('gameStarted', () => {
            console.log('游戏开始');
        });
        
        game.on('gamePaused', () => {
            console.log('游戏暂停');
            this.showPauseOverlay();
        });
        
        game.on('gameResumed', () => {
            console.log('游戏恢复');
            this.hidePauseOverlay();
        });
        
        game.on('gameStopped', () => {
            console.log('游戏停止');
        });
        
        // 场景事件
        sceneManager.on('sceneChanged', (data) => {
            console.log(`场景切换: ${data.from} -> ${data.to}`);
        });
        
        // 对话事件
        dialogManager.on('dialogShown', () => {
            // 对话显示时暂停游戏更新
            game.pause();
        });
        
        dialogManager.on('dialogHidden', () => {
            // 对话隐藏时恢复游戏
            game.resume();
        });
        
        // 音频事件
        audioManager.on('audioError', (data) => {
            console.warn('音频加载错误:', data);
        });
        
        // 图形事件
        this.graphics.on('imageError', (data) => {
            console.warn('图片加载错误:', data);
        });
        
        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.handleError(event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
            this.handleError(event.reason);
        });
    }
    
    // 开始游戏
    startGame() {
        if (!this.isInitialized) {
            console.error('游戏未初始化完成');
            return;
        }
        
        game.start();
    }
    
    // 显示加载界面
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
            this.updateLoadingText();
        }
    }
    
    // 隐藏加载界面
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
    }
    
    // 更新加载进度
    updateLoadingProgress() {
        this.currentStep++;
        this.loadingProgress = (this.currentStep / this.loadingSteps.length) * 100;
        
        const progressBar = document.querySelector('.loading-progress');
        if (progressBar) {
            progressBar.style.width = this.loadingProgress + '%';
        }
        
        this.updateLoadingText();
    }
    
    // 更新加载文本
    updateLoadingText() {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText && this.currentStep < this.loadingSteps.length) {
            loadingText.textContent = this.loadingSteps[this.currentStep];
        }
    }
    
    // 显示错误信息
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <div class="error-content">
                <h3>错误</h3>
                <p>${message}</p>
                <button onclick="location.reload()">重新加载</button>
            </div>
        `;
        document.body.appendChild(errorDiv);
    }
    
    // 显示游戏菜单
    showGameMenu() {
        const menu = document.getElementById('gameMenu');
        if (menu) {
            menu.style.display = 'block';
            game.pause();
        }
    }
    
    // 隐藏游戏菜单
    hideGameMenu() {
        const menu = document.getElementById('gameMenu');
        if (menu) {
            menu.style.display = 'none';
            game.resume();
        }
    }
    
    // 显示设置界面
    showSettings() {
        const settings = document.getElementById('settingsPanel');
        if (settings) {
            settings.style.display = 'block';
            this.loadSettingsUI();
        }
    }
    
    // 加载设置UI
    loadSettingsUI() {
        // 音量设置
        const masterVolumeSlider = document.getElementById('masterVolume');
        if (masterVolumeSlider) {
            masterVolumeSlider.value = audioManager.masterVolume;
            masterVolumeSlider.addEventListener('input', (e) => {
                audioManager.setMasterVolume(parseFloat(e.target.value));
            });
        }
        
        const musicVolumeSlider = document.getElementById('musicVolume');
        if (musicVolumeSlider) {
            musicVolumeSlider.value = audioManager.musicVolume;
            musicVolumeSlider.addEventListener('input', (e) => {
                audioManager.setMusicVolume(parseFloat(e.target.value));
            });
        }
        
        const sfxVolumeSlider = document.getElementById('sfxVolume');
        if (sfxVolumeSlider) {
            sfxVolumeSlider.value = audioManager.sfxVolume;
            sfxVolumeSlider.addEventListener('input', (e) => {
                audioManager.setSfxVolume(parseFloat(e.target.value));
            });
        }
    }
    
    // 切换道具栏显示
    toggleInventory() {
        const inventory = document.getElementById('inventory');
        if (inventory) {
            const isVisible = inventory.style.display !== 'none';
            inventory.style.display = isVisible ? 'none' : 'block';
        }
    }
    
    // 显示暂停覆盖层
    showPauseOverlay() {
        const overlay = document.getElementById('pauseOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }
    
    // 隐藏暂停覆盖层
    hidePauseOverlay() {
        const overlay = document.getElementById('pauseOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }
    
    // 保存游戏
    saveGame() {
        try {
            const saveData = {
                timestamp: Date.now(),
                version: '1.0.0',
                game: game.saveState(),
                scenes: sceneManager.saveState(),
                inventory: inventoryManager.saveState(),
                dialog: dialogManager.saveState(),
                audio: {
                    masterVolume: audioManager.masterVolume,
                    musicVolume: audioManager.musicVolume,
                    sfxVolume: audioManager.sfxVolume,
                    isMuted: audioManager.isMuted
                }
            };
            
            localStorage.setItem('fleeing_game_save', JSON.stringify(saveData));
            
            // 显示保存成功提示
            this.showNotification('游戏已保存');
            
        } catch (error) {
            console.error('保存游戏失败:', error);
            this.showNotification('保存失败: ' + error.message, 'error');
        }
    }
    
    // 加载游戏
    loadGame() {
        try {
            const saveData = localStorage.getItem('fleeing_game_save');
            if (!saveData) {
                this.showNotification('没有找到存档', 'warning');
                return;
            }
            
            const data = JSON.parse(saveData);
            
            // 加载各系统状态
            if (data.game) game.loadState(data.game);
            if (data.scenes) sceneManager.loadState(data.scenes);
            if (data.inventory) inventoryManager.loadState(data.inventory);
            if (data.dialog) dialogManager.loadState(data.dialog);
            if (data.audio) {
                audioManager.setMasterVolume(data.audio.masterVolume);
                audioManager.setMusicVolume(data.audio.musicVolume);
                audioManager.setSfxVolume(data.audio.sfxVolume);
                if (data.audio.isMuted) audioManager.mute();
            }
            
            this.showNotification('游戏已加载');
            
        } catch (error) {
            console.error('加载游戏失败:', error);
            this.showNotification('加载失败: ' + error.message, 'error');
        }
    }
    
    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    // 错误处理
    handleError(error) {
        console.error('应用程序错误:', error);

        // 尝试保存游戏状态
        try {
            this.saveGame();
        } catch (saveError) {
            console.error('紧急保存失败:', saveError);
        }

        // 显示错误信息
        this.showNotification('发生错误: ' + error.message, 'error');
    }

    // 应用设置
    applySettings() {
        const masterVolume = document.getElementById('masterVolume').value;
        const musicVolume = document.getElementById('musicVolume').value;
        const sfxVolume = document.getElementById('sfxVolume').value;
        const textSpeed = document.getElementById('textSpeed').value;
        const autoSave = document.getElementById('autoSave').checked;

        // 应用音频设置
        audioManager.setMasterVolume(parseFloat(masterVolume));
        audioManager.setMusicVolume(parseFloat(musicVolume));
        audioManager.setSfxVolume(parseFloat(sfxVolume));

        // 应用游戏设置
        game.settings.textSpeed = parseFloat(textSpeed);
        game.settings.autoSave = autoSave;

        // 更新对话管理器的打字速度
        dialogManager.typewriterSpeed = 50 / parseFloat(textSpeed);

        // 保存设置
        game.saveSettings();

        this.showNotification('设置已应用');
    }

    // 隐藏设置面板
    hideSettings() {
        const settings = document.getElementById('settingsPanel');
        if (settings) {
            settings.style.display = 'none';
        }
    }

    // 显示主菜单
    showMainMenu() {
        sceneManager.changeScene('menu', 'fade');
    }
}

// 应用程序入口点
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM加载完成，开始初始化游戏应用');
    
    // 创建应用程序实例
    const app = new GameApplication();
    window.app = app;
    
    // 初始化应用程序
    await app.init();
});

// 导出应用程序类
window.GameApplication = GameApplication;
