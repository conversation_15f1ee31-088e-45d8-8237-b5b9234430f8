// 音频管理器
class AudioManager extends EventEmitter {
    constructor() {
        super();
        this.sounds = new Map();
        this.music = new Map();
        this.currentMusic = null;
        this.musicVolume = 0.7;
        this.sfxVolume = 0.8;
        this.masterVolume = 1.0;
        this.isMuted = false;
        this.fadeInterval = null;
        
        // 音频上下文（用于更好的音频控制）
        this.audioContext = null;
        this.initAudioContext();
    }
    
    initAudioContext() {
        try {
            const AudioContextClass = window.AudioContext || window['webkitAudioContext'];
            if (AudioContextClass) {
                this.audioContext = new AudioContextClass();
            }
        } catch (e) {
            console.warn('Web Audio API not supported:', e);
        }
    }
    
    // 加载音频资源
    async loadAudio(id, src, isMusic = false) {
        try {
            const audio = await Utils.loadAudio(src);
            audio.volume = isMusic ? this.musicVolume : this.sfxVolume;
            audio.loop = isMusic;
            
            if (isMusic) {
                this.music.set(id, audio);
            } else {
                this.sounds.set(id, audio);
            }
            
            this.emit('audioLoaded', { id, isMusic });
            return audio;
        } catch (error) {
            console.error(`Failed to load audio ${id}:`, error);
            this.emit('audioError', { id, error });
            throw error;
        }
    }
    
    // 批量加载音频
    async loadAudioBatch(audioList) {
        const promises = audioList.map(({ id, src, isMusic }) => 
            this.loadAudio(id, src, isMusic)
        );
        
        try {
            await Promise.all(promises);
            this.emit('batchLoaded');
        } catch (error) {
            console.error('Failed to load audio batch:', error);
            this.emit('batchError', error);
            throw error;
        }
    }
    
    // 播放音效
    playSound(id, volume = 1.0, loop = false) {
        const sound = this.sounds.get(id);
        if (!sound) {
            console.warn(`Sound ${id} not found`);
            return null;
        }
        
        if (this.isMuted) return null;
        
        try {
            // 克隆音频对象以支持同时播放多个实例
            const audioClone = sound.cloneNode();
            audioClone.volume = volume * this.sfxVolume * this.masterVolume;
            audioClone.loop = loop;
            
            const playPromise = audioClone.play();
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.warn(`Failed to play sound ${id}:`, error);
                });
            }
            
            this.emit('soundPlayed', { id, volume, loop });
            return audioClone;
        } catch (error) {
            console.error(`Error playing sound ${id}:`, error);
            return null;
        }
    }
    
    // 播放背景音乐
    playMusic(id, fadeIn = true, fadeDuration = 1000) {
        const music = this.music.get(id);
        if (!music) {
            console.warn(`Music ${id} not found`);
            return;
        }
        
        if (this.isMuted) return;
        
        // 停止当前音乐
        if (this.currentMusic && this.currentMusic !== music) {
            this.stopMusic(true, fadeDuration / 2);
        }
        
        this.currentMusic = music;
        music.currentTime = 0;
        music.volume = fadeIn ? 0 : this.musicVolume * this.masterVolume;
        
        const playPromise = music.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                if (fadeIn) {
                    this.fadeIn(music, this.musicVolume * this.masterVolume, fadeDuration);
                }
                this.emit('musicStarted', { id, fadeIn });
            }).catch(error => {
                console.warn(`Failed to play music ${id}:`, error);
            });
        }
    }
    
    // 停止背景音乐
    stopMusic(fadeOut = true, fadeDuration = 1000) {
        if (!this.currentMusic) return;
        
        if (fadeOut) {
            this.fadeOut(this.currentMusic, fadeDuration, () => {
                this.currentMusic.pause();
                this.currentMusic = null;
                this.emit('musicStopped', { fadeOut });
            });
        } else {
            this.currentMusic.pause();
            this.currentMusic = null;
            this.emit('musicStopped', { fadeOut });
        }
    }
    
    // 暂停/恢复音乐
    pauseMusic() {
        if (this.currentMusic && !this.currentMusic.paused) {
            this.currentMusic.pause();
            this.emit('musicPaused');
        }
    }
    
    resumeMusic() {
        if (this.currentMusic && this.currentMusic.paused) {
            const playPromise = this.currentMusic.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    this.emit('musicResumed');
                }).catch(error => {
                    console.warn('Failed to resume music:', error);
                });
            }
        }
    }
    
    // 淡入效果
    fadeIn(audio, targetVolume, duration) {
        if (this.fadeInterval) {
            clearInterval(this.fadeInterval);
        }
        
        const startVolume = 0;
        const volumeStep = targetVolume / (duration / 50);
        let currentVolume = startVolume;
        
        audio.volume = startVolume;
        
        this.fadeInterval = setInterval(() => {
            currentVolume += volumeStep;
            if (currentVolume >= targetVolume) {
                currentVolume = targetVolume;
                clearInterval(this.fadeInterval);
                this.fadeInterval = null;
            }
            audio.volume = currentVolume;
        }, 50);
    }
    
    // 淡出效果
    fadeOut(audio, duration, callback) {
        if (this.fadeInterval) {
            clearInterval(this.fadeInterval);
        }
        
        const startVolume = audio.volume;
        const volumeStep = startVolume / (duration / 50);
        let currentVolume = startVolume;
        
        this.fadeInterval = setInterval(() => {
            currentVolume -= volumeStep;
            if (currentVolume <= 0) {
                currentVolume = 0;
                clearInterval(this.fadeInterval);
                this.fadeInterval = null;
                if (callback) callback();
            }
            audio.volume = currentVolume;
        }, 50);
    }
    
    // 设置音量
    setMasterVolume(volume) {
        this.masterVolume = Utils.clamp(volume, 0, 1);
        this.updateAllVolumes();
        this.emit('volumeChanged', { type: 'master', volume: this.masterVolume });
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Utils.clamp(volume, 0, 1);
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume * this.masterVolume;
        }
        this.emit('volumeChanged', { type: 'music', volume: this.musicVolume });
    }
    
    setSfxVolume(volume) {
        this.sfxVolume = Utils.clamp(volume, 0, 1);
        this.emit('volumeChanged', { type: 'sfx', volume: this.sfxVolume });
    }
    
    // 更新所有音频的音量
    updateAllVolumes() {
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume * this.masterVolume;
        }
        
        // 注意：音效的音量在播放时设置，这里不需要更新
    }
    
    // 静音/取消静音
    mute() {
        this.isMuted = true;
        if (this.currentMusic) {
            this.currentMusic.volume = 0;
        }
        this.emit('muted');
    }
    
    unmute() {
        this.isMuted = false;
        this.updateAllVolumes();
        this.emit('unmuted');
    }
    
    toggleMute() {
        if (this.isMuted) {
            this.unmute();
        } else {
            this.mute();
        }
    }
    
    // 获取音频状态
    isMusicPlaying() {
        return this.currentMusic && !this.currentMusic.paused;
    }
    
    getCurrentMusicTime() {
        return this.currentMusic ? this.currentMusic.currentTime : 0;
    }
    
    getMusicDuration() {
        return this.currentMusic ? this.currentMusic.duration : 0;
    }
    
    // 清理资源
    dispose() {
        this.stopMusic(false);
        
        if (this.fadeInterval) {
            clearInterval(this.fadeInterval);
        }
        
        this.sounds.clear();
        this.music.clear();
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.emit('disposed');
    }
    
    // 预加载游戏音频资源
    async preloadGameAudio() {
        const audioAssets = [
            // 背景音乐 - 使用占位符路径
            { id: 'menu_music', src: 'assets/audio/menu_music.mp3', isMusic: true },
            { id: 'office_music', src: 'assets/audio/office_music.mp3', isMusic: true },
            { id: 'dormitory_music', src: 'assets/audio/dormitory_music.mp3', isMusic: true },
            { id: 'escape_music', src: 'assets/audio/escape_music.mp3', isMusic: true },

            // 音效
            { id: 'success', src: 'assets/audio/success.wav', isMusic: false },
            { id: 'error', src: 'assets/audio/error.wav', isMusic: false },
            { id: 'fluorescent_hum', src: 'assets/audio/fluorescent_hum.wav', isMusic: false },
            { id: 'heartbeat', src: 'assets/audio/heartbeat.wav', isMusic: false }
        ];

        try {
            // 尝试加载音频，如果失败则创建静音占位符
            const loadPromises = audioAssets.map(async ({ id, src, isMusic }) => {
                try {
                    await this.loadAudio(id, src, isMusic);
                } catch (error) {
                    console.warn(`Failed to load audio ${id}, creating silent placeholder`);
                    // 创建静音占位符
                    this.createSilentPlaceholder(id, isMusic);
                }
            });

            await Promise.all(loadPromises);
            console.log('Game audio initialization completed (some may be placeholders)');
        } catch (error) {
            console.error('Failed to initialize game audio:', error);
            // 不抛出错误，允许游戏在静音模式下继续
        }
    }

    // 创建静音占位符
    createSilentPlaceholder(id, isMusic) {
        // 创建一个空的音频对象作为占位符
        const audioContext = this.audioContext;
        if (audioContext) {
            // 创建一个极短的静音音频
            const buffer = audioContext.createBuffer(1, 1, audioContext.sampleRate);
            const source = audioContext.createBufferSource();
            source.buffer = buffer;

            // 模拟Audio对象的接口
            const mockAudio = {
                play: () => Promise.resolve(),
                pause: () => {},
                load: () => {},
                volume: isMusic ? this.musicVolume : this.sfxVolume,
                loop: isMusic,
                currentTime: 0,
                duration: 0,
                paused: true,
                cloneNode: () => mockAudio
            };

            if (isMusic) {
                this.music.set(id, mockAudio);
            } else {
                this.sounds.set(id, mockAudio);
            }
        }
    }
}

// 创建全局音频管理器实例
window.audioManager = new AudioManager();
