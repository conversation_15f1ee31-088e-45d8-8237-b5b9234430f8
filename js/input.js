// 输入管理器
class InputManager extends EventEmitter {
    constructor(canvas) {
        super();
        this.canvas = canvas;
        this.isMouseDown = false;
        this.isTouchDevice = Utils.isTouchDevice();
        this.currentTouch = null;
        this.lastClickTime = 0;
        this.doubleClickDelay = 300;
        
        // 输入状态
        this.mousePos = { x: 0, y: 0 };
        this.lastMousePos = { x: 0, y: 0 };
        this.mouseDelta = { x: 0, y: 0 };
        
        // 拖拽状态
        this.isDragging = false;
        this.dragStart = { x: 0, y: 0 };
        this.dragCurrent = { x: 0, y: 0 };
        this.dragThreshold = 10;
        
        // 手势识别
        this.gestureStart = null;
        this.gestureDistance = 0;
        this.gestureAngle = 0;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 鼠标事件
        if (!this.isTouchDevice) {
            this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
            this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
            this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
            this.canvas.addEventListener('wheel', this.handleWheel.bind(this));
            this.canvas.addEventListener('contextmenu', this.handleContextMenu.bind(this));
        }
        
        // 触摸事件
        this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        this.canvas.addEventListener('touchcancel', this.handleTouchCancel.bind(this));
        
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // 窗口事件
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('blur', this.handleWindowBlur.bind(this));
        window.addEventListener('focus', this.handleWindowFocus.bind(this));
    }
    
    // 获取画布相对坐标
    getCanvasCoordinates(clientX, clientY) {
        return Utils.getCanvasCoordinates(this.canvas, clientX, clientY);
    }
    
    // 鼠标事件处理
    handleMouseDown(event) {
        event.preventDefault();
        this.isMouseDown = true;
        
        const coords = this.getCanvasCoordinates(event.clientX, event.clientY);
        this.mousePos = coords;
        this.lastMousePos = { ...coords };
        this.dragStart = { ...coords };
        
        // 检测双击
        const currentTime = Date.now();
        const isDoubleClick = currentTime - this.lastClickTime < this.doubleClickDelay;
        this.lastClickTime = currentTime;
        
        this.emit('mouseDown', {
            x: coords.x,
            y: coords.y,
            button: event.button,
            isDoubleClick
        });
        
        if (isDoubleClick) {
            this.emit('doubleClick', { x: coords.x, y: coords.y });
        }
    }
    
    handleMouseMove(event) {
        const coords = this.getCanvasCoordinates(event.clientX, event.clientY);
        this.lastMousePos = { ...this.mousePos };
        this.mousePos = coords;
        this.mouseDelta = {
            x: this.mousePos.x - this.lastMousePos.x,
            y: this.mousePos.y - this.lastMousePos.y
        };
        
        // 检测拖拽开始
        if (this.isMouseDown && !this.isDragging) {
            const distance = Utils.distance(
                this.dragStart.x, this.dragStart.y,
                coords.x, coords.y
            );
            
            if (distance > this.dragThreshold) {
                this.isDragging = true;
                this.emit('dragStart', {
                    startX: this.dragStart.x,
                    startY: this.dragStart.y,
                    currentX: coords.x,
                    currentY: coords.y
                });
            }
        }
        
        // 拖拽中
        if (this.isDragging) {
            this.dragCurrent = { ...coords };
            this.emit('drag', {
                startX: this.dragStart.x,
                startY: this.dragStart.y,
                currentX: coords.x,
                currentY: coords.y,
                deltaX: this.mouseDelta.x,
                deltaY: this.mouseDelta.y
            });
        }
        
        this.emit('mouseMove', {
            x: coords.x,
            y: coords.y,
            deltaX: this.mouseDelta.x,
            deltaY: this.mouseDelta.y
        });
    }
    
    handleMouseUp(event) {
        const coords = this.getCanvasCoordinates(event.clientX, event.clientY);
        
        if (this.isDragging) {
            this.emit('dragEnd', {
                startX: this.dragStart.x,
                startY: this.dragStart.y,
                endX: coords.x,
                endY: coords.y
            });
            this.isDragging = false;
        } else {
            // 普通点击
            this.emit('click', {
                x: coords.x,
                y: coords.y,
                button: event.button
            });
        }
        
        this.emit('mouseUp', {
            x: coords.x,
            y: coords.y,
            button: event.button
        });
        
        this.isMouseDown = false;
    }
    
    handleWheel(event) {
        event.preventDefault();
        const coords = this.getCanvasCoordinates(event.clientX, event.clientY);
        
        this.emit('wheel', {
            x: coords.x,
            y: coords.y,
            deltaY: event.deltaY,
            deltaX: event.deltaX
        });
    }
    
    handleContextMenu(event) {
        event.preventDefault();
        const coords = this.getCanvasCoordinates(event.clientX, event.clientY);
        
        this.emit('rightClick', {
            x: coords.x,
            y: coords.y
        });
    }
    
    // 触摸事件处理
    handleTouchStart(event) {
        event.preventDefault();
        
        if (event.touches.length === 1) {
            // 单点触摸
            const touch = event.touches[0];
            const coords = this.getCanvasCoordinates(touch.clientX, touch.clientY);
            
            this.currentTouch = touch.identifier;
            this.mousePos = coords;
            this.lastMousePos = { ...coords };
            this.dragStart = { ...coords };
            
            // 检测双击
            const currentTime = Date.now();
            const isDoubleClick = currentTime - this.lastClickTime < this.doubleClickDelay;
            this.lastClickTime = currentTime;
            
            this.emit('touchStart', {
                x: coords.x,
                y: coords.y,
                touchId: touch.identifier,
                isDoubleClick
            });
            
            if (isDoubleClick) {
                this.emit('doubleTap', { x: coords.x, y: coords.y });
            }
        } else if (event.touches.length === 2) {
            // 双点触摸（缩放/旋转手势）
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            
            const coords1 = this.getCanvasCoordinates(touch1.clientX, touch1.clientY);
            const coords2 = this.getCanvasCoordinates(touch2.clientX, touch2.clientY);
            
            this.gestureStart = {
                touch1: coords1,
                touch2: coords2,
                distance: Utils.distance(coords1.x, coords1.y, coords2.x, coords2.y),
                angle: Utils.angle(coords1.x, coords1.y, coords2.x, coords2.y)
            };
            
            this.emit('gestureStart', {
                center: {
                    x: (coords1.x + coords2.x) / 2,
                    y: (coords1.y + coords2.y) / 2
                },
                distance: this.gestureStart.distance,
                angle: this.gestureStart.angle
            });
        }
    }
    
    handleTouchMove(event) {
        event.preventDefault();
        
        if (event.touches.length === 1 && this.currentTouch !== null) {
            // 单点触摸移动
            const touch = Array.from(event.touches).find(t => t.identifier === this.currentTouch);
            if (!touch) return;
            
            const coords = this.getCanvasCoordinates(touch.clientX, touch.clientY);
            this.lastMousePos = { ...this.mousePos };
            this.mousePos = coords;
            this.mouseDelta = {
                x: this.mousePos.x - this.lastMousePos.x,
                y: this.mousePos.y - this.lastMousePos.y
            };
            
            // 检测拖拽开始
            if (!this.isDragging) {
                const distance = Utils.distance(
                    this.dragStart.x, this.dragStart.y,
                    coords.x, coords.y
                );
                
                if (distance > this.dragThreshold) {
                    this.isDragging = true;
                    this.emit('dragStart', {
                        startX: this.dragStart.x,
                        startY: this.dragStart.y,
                        currentX: coords.x,
                        currentY: coords.y
                    });
                }
            }
            
            // 拖拽中
            if (this.isDragging) {
                this.dragCurrent = { ...coords };
                this.emit('drag', {
                    startX: this.dragStart.x,
                    startY: this.dragStart.y,
                    currentX: coords.x,
                    currentY: coords.y,
                    deltaX: this.mouseDelta.x,
                    deltaY: this.mouseDelta.y
                });
            }
            
            this.emit('touchMove', {
                x: coords.x,
                y: coords.y,
                deltaX: this.mouseDelta.x,
                deltaY: this.mouseDelta.y,
                touchId: touch.identifier
            });
        } else if (event.touches.length === 2 && this.gestureStart) {
            // 双点触摸手势
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            
            const coords1 = this.getCanvasCoordinates(touch1.clientX, touch1.clientY);
            const coords2 = this.getCanvasCoordinates(touch2.clientX, touch2.clientY);
            
            const currentDistance = Utils.distance(coords1.x, coords1.y, coords2.x, coords2.y);
            const currentAngle = Utils.angle(coords1.x, coords1.y, coords2.x, coords2.y);
            
            const scale = currentDistance / this.gestureStart.distance;
            const rotation = currentAngle - this.gestureStart.angle;
            
            this.emit('gesture', {
                center: {
                    x: (coords1.x + coords2.x) / 2,
                    y: (coords1.y + coords2.y) / 2
                },
                scale,
                rotation,
                distance: currentDistance
            });
        }
    }
    
    handleTouchEnd(event) {
        event.preventDefault();
        
        if (event.touches.length === 0) {
            // 所有触摸结束
            const coords = this.mousePos;
            
            if (this.isDragging) {
                this.emit('dragEnd', {
                    startX: this.dragStart.x,
                    startY: this.dragStart.y,
                    endX: coords.x,
                    endY: coords.y
                });
                this.isDragging = false;
            } else {
                // 普通点击
                this.emit('tap', {
                    x: coords.x,
                    y: coords.y
                });
            }
            
            this.emit('touchEnd', {
                x: coords.x,
                y: coords.y
            });
            
            this.currentTouch = null;
            this.gestureStart = null;
        } else if (this.gestureStart && event.touches.length < 2) {
            // 手势结束
            this.emit('gestureEnd');
            this.gestureStart = null;
        }
    }
    
    handleTouchCancel(event) {
        this.currentTouch = null;
        this.isDragging = false;
        this.gestureStart = null;
        this.emit('touchCancel');
    }
    
    // 键盘事件处理
    handleKeyDown(event) {
        this.emit('keyDown', {
            key: event.key,
            code: event.code,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey
        });
    }
    
    handleKeyUp(event) {
        this.emit('keyUp', {
            key: event.key,
            code: event.code,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey
        });
    }
    
    // 窗口事件处理
    handleResize() {
        this.emit('resize');
    }
    
    handleWindowBlur() {
        this.emit('windowBlur');
    }
    
    handleWindowFocus() {
        this.emit('windowFocus');
    }
    
    // 清理事件监听器
    dispose() {
        // 移除所有事件监听器
        this.canvas.removeEventListener('mousedown', this.handleMouseDown);
        this.canvas.removeEventListener('mousemove', this.handleMouseMove);
        this.canvas.removeEventListener('mouseup', this.handleMouseUp);
        this.canvas.removeEventListener('wheel', this.handleWheel);
        this.canvas.removeEventListener('contextmenu', this.handleContextMenu);
        
        this.canvas.removeEventListener('touchstart', this.handleTouchStart);
        this.canvas.removeEventListener('touchmove', this.handleTouchMove);
        this.canvas.removeEventListener('touchend', this.handleTouchEnd);
        this.canvas.removeEventListener('touchcancel', this.handleTouchCancel);
        
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('blur', this.handleWindowBlur);
        window.removeEventListener('focus', this.handleWindowFocus);
        
        this.emit('disposed');
    }
}

// 创建全局输入管理器实例（将在main.js中初始化）
window.InputManager = InputManager;
