// 对话系统
class DialogManager extends EventEmitter {
    constructor() {
        super();
        this.dialogBox = document.getElementById('dialogBox');
        this.dialogText = document.getElementById('dialogText');
        this.dialogChoices = document.getElementById('dialogChoices');
        
        this.isActive = false;
        this.currentDialog = null;
        this.typewriterSpeed = 50; // 打字机效果速度（毫秒）
        this.typewriterTimer = null;
        this.isTyping = false;
        this.skipTyping = false;
        
        // 对话历史
        this.history = [];
        this.maxHistoryLength = 50;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 点击对话框跳过打字机效果
        this.dialogBox.addEventListener('click', () => {
            if (this.isTyping) {
                this.skipTyping = true;
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (this.isActive) {
                if (e.key === 'Escape') {
                    this.hide();
                } else if (e.key === 'Enter' || e.key === ' ') {
                    if (this.isTyping) {
                        this.skipTyping = true;
                    }
                }
            }
        });
    }
    
    // 显示对话
    show(dialogData) {
        this.currentDialog = dialogData;
        this.isActive = true;
        this.dialogBox.style.display = 'block';
        
        // 添加到历史记录
        this.addToHistory(dialogData);
        
        // 显示文本
        if (dialogData.text) {
            this.showText(dialogData.text);
        }
        
        // 显示选择项
        if (dialogData.choices && dialogData.choices.length > 0) {
            this.showChoices(dialogData.choices);
        } else {
            this.dialogChoices.innerHTML = '';
        }
        
        this.emit('dialogShown', dialogData);
    }
    
    // 隐藏对话
    hide() {
        this.isActive = false;
        this.dialogBox.style.display = 'none';
        this.currentDialog = null;
        
        if (this.typewriterTimer) {
            clearTimeout(this.typewriterTimer);
            this.typewriterTimer = null;
        }
        
        this.isTyping = false;
        this.skipTyping = false;
        
        this.emit('dialogHidden');
    }
    
    // 显示文本（打字机效果）
    showText(text) {
        this.dialogText.innerHTML = '';
        this.isTyping = true;
        this.skipTyping = false;
        
        let currentIndex = 0;
        const showNextChar = () => {
            if (this.skipTyping || currentIndex >= text.length) {
                // 跳过或完成打字
                this.dialogText.innerHTML = this.formatText(text);
                this.isTyping = false;
                this.skipTyping = false;
                this.emit('textComplete');
                return;
            }
            
            const char = text[currentIndex];
            this.dialogText.innerHTML = this.formatText(text.substring(0, currentIndex + 1));
            currentIndex++;
            
            // 根据字符类型调整速度
            let delay = this.typewriterSpeed;
            if (char === '.' || char === '!' || char === '?') {
                delay *= 3; // 标点符号后停顿更长
            } else if (char === ',' || char === ';') {
                delay *= 2;
            } else if (char === ' ') {
                delay *= 0.5; // 空格更快
            }
            
            this.typewriterTimer = setTimeout(showNextChar, delay);
        };
        
        showNextChar();
    }
    
    // 格式化文本（支持简单的标记）
    formatText(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
            .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
            .replace(/__(.*?)__/g, '<u>$1</u>') // 下划线
            .replace(/\n/g, '<br>'); // 换行
    }
    
    // 显示选择项
    showChoices(choices) {
        this.dialogChoices.innerHTML = '';
        
        choices.forEach((choice, index) => {
            const choiceElement = document.createElement('div');
            choiceElement.className = 'dialog-choice';
            choiceElement.innerHTML = this.formatText(choice.text);
            
            // 添加选择项样式
            if (choice.type) {
                choiceElement.classList.add(`choice-${choice.type}`);
            }
            
            // 检查是否可用
            if (choice.condition && !choice.condition()) {
                choiceElement.classList.add('disabled');
                choiceElement.style.opacity = '0.5';
                choiceElement.style.cursor = 'not-allowed';
            } else {
                choiceElement.addEventListener('click', () => {
                    this.selectChoice(choice, index);
                });
                
                // 键盘快捷键（数字键）
                if (index < 9) {
                    const keyHandler = (e) => {
                        if (this.isActive && e.key === (index + 1).toString()) {
                            this.selectChoice(choice, index);
                            document.removeEventListener('keydown', keyHandler);
                        }
                    };
                    document.addEventListener('keydown', keyHandler);
                }
            }
            
            // 添加快捷键提示
            if (index < 9) {
                const keyHint = document.createElement('span');
                keyHint.textContent = `[${index + 1}]`;
                keyHint.style.opacity = '0.7';
                keyHint.style.fontSize = '12px';
                keyHint.style.marginRight = '8px';
                choiceElement.insertBefore(keyHint, choiceElement.firstChild);
            }
            
            this.dialogChoices.appendChild(choiceElement);
        });
    }
    
    // 选择选项
    selectChoice(choice, index) {
        // 执行选择效果
        if (choice.action) {
            choice.action();
        }
        
        // 更新游戏状态
        if (choice.effects) {
            this.applyChoiceEffects(choice.effects);
        }
        
        this.emit('choiceSelected', { choice, index });
        
        // 继续对话或关闭
        if (choice.next) {
            if (typeof choice.next === 'function') {
                const nextDialog = choice.next();
                if (nextDialog) {
                    this.show(nextDialog);
                } else {
                    this.hide();
                }
            } else {
                this.show(choice.next);
            }
        } else {
            this.hide();
        }
    }
    
    // 应用选择效果
    applyChoiceEffects(effects) {
        if (effects.suspicion && window.game) {
            window.game.changeSuspicion(effects.suspicion);
        }

        if (effects.performance && window.game) {
            window.game.changePerformance(effects.performance);
        }

        if (effects.addItem && window.inventoryManager) {
            window.inventoryManager.addItem(effects.addItem.id, effects.addItem.quantity || 1);
        }

        if (effects.removeItem && window.inventoryManager) {
            window.inventoryManager.removeItem(effects.removeItem.id, effects.removeItem.quantity || 1);
        }

        if (effects.setFlag && window.game) {
            Object.keys(effects.setFlag).forEach(flag => {
                window.game.setFlag(flag, effects.setFlag[flag]);
            });
        }

        if (effects.playSound && window.audioManager) {
            window.audioManager.playSound(effects.playSound);
        }

        if (effects.changeScene && window.sceneManager) {
            window.sceneManager.changeScene(effects.changeScene);
        }

        this.emit('effectsApplied', effects);
    }
    
    // 添加到历史记录
    addToHistory(dialogData) {
        this.history.push({
            timestamp: Date.now(),
            speaker: dialogData.speaker || 'Unknown',
            text: dialogData.text || '',
            choices: dialogData.choices ? dialogData.choices.map(c => c.text) : []
        });
        
        // 限制历史记录长度
        if (this.history.length > this.maxHistoryLength) {
            this.history.shift();
        }
    }
    
    // 获取历史记录
    getHistory() {
        return [...this.history];
    }
    
    // 清空历史记录
    clearHistory() {
        this.history = [];
        this.emit('historyClear');
    }
    
    // 创建简单对话
    createSimpleDialog(text, speaker = null) {
        return {
            speaker,
            text,
            choices: [{
                text: '继续',
                action: null
            }]
        };
    }
    
    // 创建选择对话
    createChoiceDialog(text, choices, speaker = null) {
        return {
            speaker,
            text,
            choices
        };
    }
    
    // 创建道德选择对话
    createMoralChoiceDialog(text, choices, speaker = null) {
        const moralChoices = choices.map(choice => ({
            ...choice,
            type: choice.moral || 'neutral'
        }));
        
        return {
            speaker,
            text,
            choices: moralChoices
        };
    }
    
    // 预定义的对话模板
    getDialogTemplates() {
        return {
            // 第一次诈骗任务
            firstScamTask: {
                speaker: '主管',
                text: '新人，这是你的第一个任务。按照话术本给这个号码打电话，记住，要让他们相信你。',
                choices: [
                    {
                        text: '我不能做这种事情...',
                        type: 'moral',
                        effects: { suspicion: 5 },
                        action: () => audioManager.playSound('error')
                    },
                    {
                        text: '我明白了，我会尽力的。',
                        type: 'compliance',
                        effects: { suspicion: -2, performance: 1 },
                        action: () => audioManager.playSound('success')
                    },
                    {
                        text: '能不能先让我熟悉一下环境？',
                        type: 'delay',
                        effects: { suspicion: 1 },
                        next: () => this.getDialogTemplates().supervisorImpatient
                    }
                ]
            },
            
            supervisorImpatient: {
                speaker: '主管',
                text: '没时间让你慢慢适应！这里不是度假村，要么干活，要么滚蛋！',
                choices: [
                    {
                        text: '好的，我马上开始工作。',
                        type: 'compliance',
                        effects: { suspicion: -1, performance: 1 }
                    },
                    {
                        text: '我需要考虑一下...',
                        type: 'resistance',
                        effects: { suspicion: 8 },
                        action: () => game.triggerBadEvent('supervisor_angry')
                    }
                ]
            },
            
            // 室友对话
            roommateHelp: {
                speaker: '室友小王',
                text: '兄弟，我看你是新来的。这里的水很深，要小心啊...',
                choices: [
                    {
                        text: '你能告诉我更多吗？',
                        type: 'inquiry',
                        next: () => this.getDialogTemplates().roommateAdvice
                    },
                    {
                        text: '我会注意的，谢谢。',
                        type: 'grateful',
                        effects: { setFlag: { roommate_trust: 1 } }
                    },
                    {
                        text: '我不需要你的建议。',
                        type: 'dismissive',
                        effects: { setFlag: { roommate_trust: -1 } }
                    }
                ]
            },
            
            roommateAdvice: {
                speaker: '室友小王',
                text: '这里的人分两种，一种是被迫的，一种是主动的。你要学会分辨，也要学会保护自己。',
                choices: [
                    {
                        text: '有什么保护自己的方法吗？',
                        type: 'inquiry',
                        effects: { setFlag: { knows_survival_tips: true } },
                        action: () => inventoryManager.addItem('survival_tips')
                    },
                    {
                        text: '我明白了，谢谢你的提醒。',
                        type: 'understanding',
                        effects: { setFlag: { roommate_trust: 2 } }
                    }
                ]
            }
        };
    }
    
    // 保存/加载状态
    saveState() {
        return {
            history: this.history,
            isActive: this.isActive,
            currentDialog: this.currentDialog
        };
    }
    
    loadState(state) {
        this.history = state.history || [];
        
        if (state.isActive && state.currentDialog) {
            this.show(state.currentDialog);
        } else {
            this.hide();
        }
    }
}

// 创建全局对话管理器实例
window.dialogManager = new DialogManager();
