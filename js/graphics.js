// 图形渲染管理器
class GraphicsManager extends EventEmitter {
    constructor(canvas) {
        super();
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.images = new Map();
        this.animations = new Map();
        this.particles = [];
        
        // 渲染设置
        this.pixelRatio = window.devicePixelRatio || 1;
        this.setupCanvas();
        
        // 动画帧
        this.lastFrameTime = 0;
        this.deltaTime = 0;
        this.fps = 0;
        this.frameCount = 0;
        
        // 相机系统
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1,
            targetX: 0,
            targetY: 0,
            targetZoom: 1,
            smoothing: 0.1
        };
        
        // 效果系统
        this.effects = {
            shake: { x: 0, y: 0, intensity: 0, duration: 0 },
            fade: { alpha: 1, target: 1, speed: 0 },
            flash: { alpha: 0, color: '#ffffff', duration: 0 }
        };
    }
    
    // 设置画布
    setupCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        this.canvas.width = rect.width * this.pixelRatio;
        this.canvas.height = rect.height * this.pixelRatio;
        this.ctx.scale(this.pixelRatio, this.pixelRatio);
        
        // 设置渲染质量
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = 'high';
    }
    
    // 调整画布大小
    resize() {
        this.setupCanvas();
        this.emit('resize', { 
            width: this.canvas.width, 
            height: this.canvas.height 
        });
    }
    
    // 加载图片
    async loadImage(id, src) {
        try {
            const img = await Utils.loadImage(src);
            this.images.set(id, img);
            this.emit('imageLoaded', { id, img });
            return img;
        } catch (error) {
            console.error(`Failed to load image ${id}:`, error);
            this.emit('imageError', { id, error });
            throw error;
        }
    }
    
    // 批量加载图片
    async loadImageBatch(imageList) {
        const promises = imageList.map(({ id, src }) => 
            this.loadImage(id, src)
        );
        
        try {
            await Promise.all(promises);
            this.emit('batchLoaded');
        } catch (error) {
            console.error('Failed to load image batch:', error);
            this.emit('batchError', error);
            throw error;
        }
    }
    
    // 获取图片
    getImage(id) {
        return this.images.get(id);
    }
    
    // 清空画布
    clear(color = null) {
        if (color) {
            this.ctx.fillStyle = color;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        } else {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }
    
    // 绘制图片
    drawImage(id, x, y, width = null, height = null, alpha = 1) {
        const img = this.images.get(id);
        if (!img) {
            console.warn(`Image ${id} not found`);
            return;
        }
        
        const oldAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = alpha;
        
        if (width !== null && height !== null) {
            this.ctx.drawImage(img, x, y, width, height);
        } else {
            this.ctx.drawImage(img, x, y);
        }
        
        this.ctx.globalAlpha = oldAlpha;
    }
    
    // 绘制图片的一部分
    drawImagePart(id, sx, sy, sw, sh, dx, dy, dw, dh, alpha = 1) {
        const img = this.images.get(id);
        if (!img) {
            console.warn(`Image ${id} not found`);
            return;
        }
        
        const oldAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = alpha;
        
        this.ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh);
        
        this.ctx.globalAlpha = oldAlpha;
    }
    
    // 绘制矩形
    drawRect(x, y, width, height, color, filled = true) {
        this.ctx.fillStyle = color;
        this.ctx.strokeStyle = color;
        
        if (filled) {
            this.ctx.fillRect(x, y, width, height);
        } else {
            this.ctx.strokeRect(x, y, width, height);
        }
    }
    
    // 绘制圆形
    drawCircle(x, y, radius, color, filled = true) {
        this.ctx.beginPath();
        this.ctx.arc(x, y, radius, 0, Math.PI * 2);
        
        if (filled) {
            this.ctx.fillStyle = color;
            this.ctx.fill();
        } else {
            this.ctx.strokeStyle = color;
            this.ctx.stroke();
        }
    }
    
    // 绘制文本
    drawText(text, x, y, options = {}) {
        const {
            font = '16px Arial',
            color = '#000000',
            align = 'left',
            baseline = 'top',
            maxWidth = null,
            stroke = false,
            strokeColor = '#000000',
            strokeWidth = 1
        } = options;
        
        this.ctx.font = font;
        this.ctx.fillStyle = color;
        this.ctx.textAlign = align;
        this.ctx.textBaseline = baseline;
        
        if (stroke) {
            this.ctx.strokeStyle = strokeColor;
            this.ctx.lineWidth = strokeWidth;
            if (maxWidth) {
                this.ctx.strokeText(text, x, y, maxWidth);
            } else {
                this.ctx.strokeText(text, x, y);
            }
        }
        
        if (maxWidth) {
            this.ctx.fillText(text, x, y, maxWidth);
        } else {
            this.ctx.fillText(text, x, y);
        }
    }
    
    // 绘制高亮效果
    drawHighlight(x, y, width, height, color = '#ffff00', alpha = 0.3) {
        const oldAlpha = this.ctx.globalAlpha;
        this.ctx.globalAlpha = alpha;
        
        // 绘制发光边框
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 3;
        this.ctx.strokeRect(x - 2, y - 2, width + 4, height + 4);
        
        // 绘制内部高亮
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x, y, width, height);
        
        this.ctx.globalAlpha = oldAlpha;
    }
    
    // 相机控制
    setCameraTarget(x, y, zoom = 1) {
        this.camera.targetX = x;
        this.camera.targetY = y;
        this.camera.targetZoom = zoom;
    }
    
    updateCamera() {
        this.camera.x = Utils.lerp(this.camera.x, this.camera.targetX, this.camera.smoothing);
        this.camera.y = Utils.lerp(this.camera.y, this.camera.targetY, this.camera.smoothing);
        this.camera.zoom = Utils.lerp(this.camera.zoom, this.camera.targetZoom, this.camera.smoothing);
    }
    
    // 应用相机变换
    applyCameraTransform() {
        this.ctx.save();
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
        this.ctx.translate(-this.camera.x, -this.camera.y);
        
        // 应用屏幕震动效果
        if (this.effects.shake.intensity > 0) {
            const shakeX = (Math.random() - 0.5) * this.effects.shake.intensity;
            const shakeY = (Math.random() - 0.5) * this.effects.shake.intensity;
            this.ctx.translate(shakeX, shakeY);
        }
    }
    
    // 恢复相机变换
    restoreCameraTransform() {
        this.ctx.restore();
    }
    
    // 屏幕震动效果
    shake(intensity, duration) {
        this.effects.shake.intensity = intensity;
        this.effects.shake.duration = duration;
    }
    
    // 淡入淡出效果
    fadeToColor(color, duration, callback) {
        this.effects.fade.target = 0;
        this.effects.fade.speed = 1 / (duration * 60); // 假设60FPS
        this.effects.fade.callback = callback;
        this.effects.fade.color = color;
    }
    
    fadeFromColor(duration, callback) {
        this.effects.fade.target = 1;
        this.effects.fade.speed = 1 / (duration * 60);
        this.effects.fade.callback = callback;
    }
    
    // 闪光效果
    flash(color = '#ffffff', duration = 0.2) {
        this.effects.flash.color = color;
        this.effects.flash.alpha = 1;
        this.effects.flash.duration = duration * 60; // 转换为帧数
    }
    
    // 更新效果
    updateEffects(deltaTime) {
        // 更新震动效果
        if (this.effects.shake.duration > 0) {
            this.effects.shake.duration -= deltaTime;
            if (this.effects.shake.duration <= 0) {
                this.effects.shake.intensity = 0;
            }
        }
        
        // 更新淡入淡出效果
        if (this.effects.fade.alpha !== this.effects.fade.target) {
            const direction = this.effects.fade.target > this.effects.fade.alpha ? 1 : -1;
            this.effects.fade.alpha += this.effects.fade.speed * direction;
            
            if ((direction > 0 && this.effects.fade.alpha >= this.effects.fade.target) ||
                (direction < 0 && this.effects.fade.alpha <= this.effects.fade.target)) {
                this.effects.fade.alpha = this.effects.fade.target;
                if (this.effects.fade.callback) {
                    this.effects.fade.callback();
                    this.effects.fade.callback = null;
                }
            }
        }
        
        // 更新闪光效果
        if (this.effects.flash.duration > 0) {
            this.effects.flash.duration--;
            this.effects.flash.alpha = this.effects.flash.duration / 60;
        }
    }
    
    // 渲染效果
    renderEffects() {
        // 渲染淡入淡出
        if (this.effects.fade.alpha < 1) {
            this.ctx.fillStyle = this.effects.fade.color || '#000000';
            this.ctx.globalAlpha = 1 - this.effects.fade.alpha;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.globalAlpha = 1;
        }
        
        // 渲染闪光
        if (this.effects.flash.alpha > 0) {
            this.ctx.fillStyle = this.effects.flash.color;
            this.ctx.globalAlpha = this.effects.flash.alpha;
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.globalAlpha = 1;
        }
    }
    
    // 更新和渲染
    update(currentTime) {
        this.deltaTime = currentTime - this.lastFrameTime;
        this.lastFrameTime = currentTime;
        
        // 计算FPS
        this.frameCount++;
        if (this.frameCount % 60 === 0) {
            this.fps = Math.round(1000 / this.deltaTime);
        }
        
        this.updateCamera();
        this.updateEffects(this.deltaTime / 1000);
    }
    
    // 预加载游戏图片资源
    async preloadGameImages() {
        const imageAssets = [
            // 场景背景
            { id: 'menu_bg', src: 'assets/images/menu_bg.jpg' },
            { id: 'office_bg', src: 'assets/images/office_bg.jpg' },
            { id: 'dormitory_bg', src: 'assets/images/dormitory_bg.jpg' },
            { id: 'corridor_bg', src: 'assets/images/corridor_bg.jpg' },
            { id: 'exit_bg', src: 'assets/images/exit_bg.jpg' },

            // 角色
            { id: 'character_normal', src: 'assets/images/character_normal.png' },
            { id: 'character_worried', src: 'assets/images/character_worried.png' },
            { id: 'character_determined', src: 'assets/images/character_determined.png' },
            { id: 'character_scared', src: 'assets/images/character_scared.png' },

            // UI按钮
            { id: 'btn_start', src: 'assets/images/btn_start.png' },
            { id: 'btn_settings', src: 'assets/images/btn_settings.png' },
            { id: 'btn_inventory', src: 'assets/images/btn_inventory.png' },
            { id: 'btn_menu', src: 'assets/images/btn_menu.png' }
        ];

        try {
            // 尝试加载图片，如果失败则创建占位符
            const loadPromises = imageAssets.map(async ({ id, src }) => {
                try {
                    await this.loadImage(id, src);
                } catch (error) {
                    console.warn(`Failed to load image ${id}, creating placeholder`);
                    this.createImagePlaceholder(id);
                }
            });

            await Promise.all(loadPromises);
            console.log('Game images initialization completed (some may be placeholders)');
        } catch (error) {
            console.error('Failed to initialize game images:', error);
            // 不抛出错误，允许游戏继续运行
        }
    }

    // 创建图片占位符
    createImagePlaceholder(id) {
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;

        const ctx = canvas.getContext('2d');

        // 根据图片类型设置不同的占位符
        if (id.includes('bg')) {
            // 背景图片 - 渐变色
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#2c3e50');
            gradient.addColorStop(1, '#34495e');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加文字标识
            ctx.fillStyle = '#ecf0f1';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(id.replace('_', ' ').toUpperCase(), canvas.width / 2, canvas.height / 2);
        } else if (id.includes('character')) {
            // 角色图片 - 简单人形
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(canvas.width / 2 - 30, 50, 60, 80); // 身体
            ctx.beginPath();
            ctx.arc(canvas.width / 2, 40, 25, 0, Math.PI * 2); // 头部
            ctx.fill();

            // 表情标识
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(id.split('_')[1], canvas.width / 2, canvas.height - 20);
        } else {
            // 按钮和其他图片 - 简单矩形
            ctx.fillStyle = '#3498db';
            ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
            ctx.strokeStyle = '#2980b9';
            ctx.lineWidth = 3;
            ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);

            ctx.fillStyle = '#ffffff';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(id.replace('_', ' '), canvas.width / 2, canvas.height / 2);
        }

        // 将canvas转换为图片并存储
        const img = new Image();
        img.src = canvas.toDataURL();
        this.images.set(id, img);
    }
}

// 创建全局图形管理器实例（将在main.js中初始化）
window.GraphicsManager = GraphicsManager;
