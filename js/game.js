// 游戏核心系统
class Game extends EventEmitter {
    constructor() {
        super();
        
        // 游戏状态
        this.isRunning = false;
        this.isPaused = false;
        this.debugMode = false;
        
        // 游戏数据
        this.suspicion = 30; // 怀疑值 0-100
        this.performance = 0; // 业绩值
        this.day = 1;
        this.hour = 9; // 24小时制
        this.flags = new Map(); // 游戏标志
        
        // 游戏设置
        this.settings = {
            masterVolume: 1.0,
            musicVolume: 0.7,
            sfxVolume: 0.8,
            textSpeed: 1.0,
            autoSave: true,
            difficulty: 'normal'
        };
        
        // UI元素
        this.statusBar = document.getElementById('statusBar');
        this.suspicionMeter = document.querySelector('.suspicion-fill');
        this.performanceMeter = document.querySelector('.performance-fill');
        this.timeDisplay = document.getElementById('timeDisplay');
        
        // 游戏计时器
        this.gameTimer = null;
        this.autoSaveTimer = null;
        
        this.setupEventListeners();
        this.loadSettings();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 暂停/恢复
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.togglePause();
            } else if (e.key === 'F1') {
                this.toggleDebugMode();
            }
        });
        
        // 窗口失焦时暂停
        window.addEventListener('blur', () => {
            if (this.isRunning) {
                this.pause();
            }
        });
        
        // 窗口聚焦时恢复
        window.addEventListener('focus', () => {
            if (this.isPaused) {
                this.resume();
            }
        });
        
        // 页面关闭前保存
        window.addEventListener('beforeunload', () => {
            this.saveGame();
        });
    }
    
    // 初始化游戏
    async init() {
        try {
            // 预加载资源
            await this.preloadResources();
            
            // 初始化道具系统
            this.initializeItems();
            
            // 创建场景
            this.initializeScenes();
            
            // 更新UI
            this.updateUI();
            
            this.emit('initialized');
            return true;
        } catch (error) {
            console.error('Game initialization failed:', error);
            this.emit('initError', error);
            return false;
        }
    }
    
    // 预加载资源
    async preloadResources() {
        const loadingPromises = [
            audioManager.preloadGameAudio(),
            graphics.preloadGameImages()
        ];
        
        await Promise.all(loadingPromises);
    }
    
    // 初始化道具
    initializeItems() {
        // 定义游戏道具
        inventoryManager.defineItem('phone', {
            name: '手机',
            description: '一部普通的智能手机，可以用来联系外界。',
            icon: 'icons/phone.png',
            usable: true,
            onUse: () => this.usePhone()
        });
        
        inventoryManager.defineItem('id_card', {
            name: '身份证',
            description: '你的身份证明，逃跑时可能会用到。',
            icon: 'icons/id_card.png'
        });
        
        inventoryManager.defineItem('money', {
            name: '现金',
            description: '一些现金，可以用来贿赂或购买物品。',
            icon: 'icons/money.png',
            stackable: true,
            maxStack: 999
        });
        
        inventoryManager.defineItem('key_card', {
            name: '门禁卡',
            description: '可以打开某些门的门禁卡。',
            icon: 'icons/key_card.png',
            usable: true,
            onUse: (item, slot) => this.useKeyCard()
        });
        
        inventoryManager.defineItem('evidence', {
            name: '证据',
            description: '可以证明诈骗活动的证据材料。',
            icon: 'icons/evidence.png',
            combinable: true,
            combinesWith: ['phone'],
            onCombine: () => ({ id: 'evidence_backup', quantity: 1 })
        });
        
        inventoryManager.defineItem('survival_tips', {
            name: '生存指南',
            description: '室友给的生存建议，包含一些有用的信息。',
            icon: 'icons/tips.png',
            usable: true,
            onUse: () => this.showSurvivalTips()
        });
    }
    
    // 初始化场景
    initializeScenes() {
        const scenes = createGameScenes();
        scenes.forEach(scene => {
            sceneManager.registerScene(scene);
        });
        
        // 设置初始场景
        sceneManager.changeScene('menu', 'none');
    }
    
    // 开始游戏
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.isPaused = false;
        
        // 开始游戏循环
        this.startGameLoop();
        
        // 开始游戏计时器
        this.startGameTimer();
        
        // 开始自动保存
        if (this.settings.autoSave) {
            this.startAutoSave();
        }
        
        this.emit('gameStarted');
    }
    
    // 暂停游戏
    pause() {
        if (!this.isRunning || this.isPaused) return;
        
        this.isPaused = true;
        audioManager.pauseMusic();
        
        this.emit('gamePaused');
    }
    
    // 恢复游戏
    resume() {
        if (!this.isRunning || !this.isPaused) return;
        
        this.isPaused = false;
        audioManager.resumeMusic();
        
        this.emit('gameResumed');
    }
    
    // 切换暂停状态
    togglePause() {
        if (this.isPaused) {
            this.resume();
        } else {
            this.pause();
        }
    }
    
    // 停止游戏
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        this.isPaused = false;
        
        // 停止计时器
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
        
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
        
        // 停止音频
        audioManager.stopMusic();
        
        this.emit('gameStopped');
    }
    
    // 开始游戏循环
    startGameLoop() {
        let lastTime = 0;
        
        const gameLoop = (currentTime) => {
            if (!this.isRunning) return;
            
            const deltaTime = currentTime - lastTime;
            lastTime = currentTime;
            
            if (!this.isPaused) {
                this.update(deltaTime);
                this.render();
            }
            
            requestAnimationFrame(gameLoop);
        };
        
        requestAnimationFrame(gameLoop);
    }
    
    // 更新游戏状态
    update(deltaTime) {
        // 更新场景
        sceneManager.update(deltaTime);
        
        // 更新图形效果
        graphics.update(Date.now());
        
        // 检查游戏结束条件
        this.checkGameEndConditions();
        
        // 更新UI
        this.updateUI();
    }
    
    // 渲染游戏
    render() {
        // 清空画布
        graphics.clear('#000000');
        
        // 应用相机变换
        graphics.applyCameraTransform();
        
        // 渲染场景
        sceneManager.render(graphics);
        
        // 恢复相机变换
        graphics.restoreCameraTransform();
        
        // 渲染UI效果
        graphics.renderEffects();
        
        // 渲染调试信息
        if (this.debugMode) {
            this.renderDebugInfo();
        }
    }
    
    // 开始游戏计时器
    startGameTimer() {
        this.gameTimer = setInterval(() => {
            if (!this.isPaused) {
                this.advanceTime(1); // 每秒游戏时间推进1小时
            }
        }, 1000);
    }
    
    // 推进游戏时间
    advanceTime(hours) {
        this.hour += hours;
        
        if (this.hour >= 24) {
            this.hour = 0;
            this.day++;
            this.emit('newDay', this.day);
        }
        
        this.emit('timeChanged', { day: this.day, hour: this.hour });
    }
    
    // 改变怀疑值
    changeSuspicion(amount) {
        const oldValue = this.suspicion;
        this.suspicion = Utils.clamp(this.suspicion + amount, 0, 100);
        
        if (this.suspicion !== oldValue) {
            this.emit('suspicionChanged', { 
                old: oldValue, 
                new: this.suspicion, 
                change: amount 
            });
            
            // 检查怀疑值阈值
            this.checkSuspicionThresholds();
        }
    }
    
    // 改变业绩值
    changePerformance(amount) {
        const oldValue = this.performance;
        this.performance = Math.max(0, this.performance + amount);
        
        if (this.performance !== oldValue) {
            this.emit('performanceChanged', { 
                old: oldValue, 
                new: this.performance, 
                change: amount 
            });
        }
    }
    
    // 检查怀疑值阈值
    checkSuspicionThresholds() {
        if (this.suspicion >= 80 && !this.getFlag('high_suspicion_warning')) {
            this.setFlag('high_suspicion_warning', true);
            this.triggerEvent('high_suspicion');
        } else if (this.suspicion >= 60 && !this.getFlag('medium_suspicion_warning')) {
            this.setFlag('medium_suspicion_warning', true);
            this.triggerEvent('medium_suspicion');
        }
    }
    
    // 检查游戏结束条件
    checkGameEndConditions() {
        // 怀疑值过高
        if (this.suspicion >= 100) {
            this.triggerGameEnd('caught');
        }
        
        // 成功逃脱
        if (this.getFlag('escaped_successfully')) {
            this.triggerGameEnd('escaped');
        }
        
        // 其他结局条件...
    }
    
    // 触发游戏结束
    triggerGameEnd(endingType) {
        this.stop();
        this.showEnding(endingType);
    }
    
    // 显示结局
    showEnding(endingType) {
        const endings = {
            caught: {
                title: '被发现',
                text: '你的行为引起了过多的怀疑，最终被发现了...',
                image: 'endings/caught.jpg'
            },
            escaped: {
                title: '成功逃脱',
                text: '你成功逃出了缅北园区，重获自由！',
                image: 'endings/escaped.jpg'
            }
        };
        
        const ending = endings[endingType];
        if (ending) {
            // 显示结局界面
            this.showEndingScreen(ending);
        }
    }
    
    // 更新UI
    updateUI() {
        // 更新怀疑值显示
        if (this.suspicionMeter) {
            this.suspicionMeter.style.width = `${this.suspicion}%`;
            
            // 根据怀疑值改变颜色
            if (this.suspicion >= 80) {
                this.suspicionMeter.style.backgroundColor = '#ff4444';
            } else if (this.suspicion >= 60) {
                this.suspicionMeter.style.backgroundColor = '#ffaa44';
            } else {
                this.suspicionMeter.style.backgroundColor = '#44ff44';
            }
        }
        
        // 更新业绩值显示
        if (this.performanceMeter) {
            const maxPerformance = 100; // 假设最大业绩值
            const percentage = Math.min(100, (this.performance / maxPerformance) * 100);
            this.performanceMeter.style.width = `${percentage}%`;
        }
        
        // 更新时间显示
        if (this.timeDisplay) {
            this.timeDisplay.textContent = `第${this.day}天 ${this.hour.toString().padStart(2, '0')}:00`;
        }
    }
    
    // 设置标志
    setFlag(flag, value) {
        this.flags.set(flag, value);
        this.emit('flagSet', { flag, value });
    }
    
    // 获取标志
    getFlag(flag, defaultValue = false) {
        return this.flags.get(flag) || defaultValue;
    }
    
    // 触发事件
    triggerEvent(eventType, data = {}) {
        this.emit('gameEvent', { type: eventType, data });
        
        // 处理特定事件
        switch (eventType) {
            case 'high_suspicion':
                this.handleHighSuspicion();
                break;
            case 'medium_suspicion':
                this.handleMediumSuspicion();
                break;
        }
    }
    
    // 处理高怀疑值事件
    handleHighSuspicion() {
        if (window.audioManager) {
            window.audioManager.playSound('heartbeat', 0.5, true);
        }
        if (window.graphics) {
            window.graphics.shake(5, 2);
        }

        if (window.dialogManager) {
            window.dialogManager.show({
                text: '你感到周围的人都在注视着你，心跳加速...',
                choices: [{
                    text: '保持冷静',
                    effects: { suspicion: -2 }
                }]
            });
        }
    }
    
    // 处理中等怀疑值事件
    handleMediumSuspicion() {
        dialogManager.show({
            text: '你注意到主管开始更频繁地检查你的工作...',
            choices: [{
                text: '更加小心',
                effects: { suspicion: -1 }
            }]
        });
    }
    
    // 开始自动保存
    startAutoSave() {
        this.autoSaveTimer = setInterval(() => {
            this.saveGame('auto');
        }, 60000); // 每分钟自动保存
    }
    
    // 切换调试模式
    toggleDebugMode() {
        this.debugMode = !this.debugMode;
        this.emit('debugModeToggled', this.debugMode);
    }
    
    // 渲染调试信息
    renderDebugInfo() {
        const debugInfo = [
            `FPS: ${graphics.fps}`,
            `Scene: ${sceneManager.currentScene?.id}`,
            `Suspicion: ${this.suspicion}`,
            `Performance: ${this.performance}`,
            `Day: ${this.day}, Hour: ${this.hour}`,
            `Flags: ${this.flags.size}`
        ];

        debugInfo.forEach((info, index) => {
            graphics.drawText(info, 10, 10 + index * 20, {
                font: '14px monospace',
                color: '#00ff00',
                stroke: true,
                strokeColor: '#000000'
            });
        });
    }

    // 游戏特定方法
    usePhone() {
        if (this.getFlag('phone_confiscated')) {
            dialogManager.show({
                text: '你的手机已经被没收了。',
                choices: [{ text: '确定', action: null }]
            });
            return false;
        }

        dialogManager.show({
            text: '你想用手机做什么？',
            choices: [
                {
                    text: '联系家人',
                    action: () => this.contactFamily(),
                    effects: { suspicion: 3 }
                },
                {
                    text: '查看新闻',
                    action: () => this.checkNews()
                },
                {
                    text: '收起手机',
                    action: null
                }
            ]
        });
        return true;
    }

    useKeyCard() {
        const currentScene = sceneManager.currentScene;
        if (currentScene && currentScene.id === 'office') {
            dialogManager.show({
                text: '你使用门禁卡打开了一扇锁着的门。',
                choices: [{ text: '进入', action: () => sceneManager.changeScene('restricted_area') }]
            });
            return true;
        }

        dialogManager.show({
            text: '这里没有可以使用门禁卡的地方。',
            choices: [{ text: '确定', action: null }]
        });
        return false;
    }

    showSurvivalTips() {
        dialogManager.show({
            text: '生存指南：\n1. 保持低调，不要引起注意\n2. 观察周围环境，寻找逃跑路线\n3. 收集有用的物品和信息\n4. 与其他被困者建立信任关系',
            choices: [{ text: '明白了', action: null }]
        });
        return true;
    }

    startScamTask() {
        this.setFlag('working', true);
        this.changePerformance(2);
        this.changeSuspicion(-1);

        dialogManager.show({
            text: '你开始按照话术本进行诈骗通话...',
            choices: [
                {
                    text: '认真执行',
                    effects: { performance: 3, suspicion: -2 }
                },
                {
                    text: '敷衍了事',
                    effects: { performance: 1, suspicion: 1 }
                },
                {
                    text: '故意搞砸',
                    effects: { performance: -1, suspicion: 5 }
                }
            ]
        });
    }

    fakeWork() {
        this.setFlag('fake_working', true);
        this.changeSuspicion(2);

        dialogManager.show({
            text: '你假装在工作，但实际上在观察周围的环境...',
            choices: [{ text: '继续观察', action: () => this.observeEnvironment() }]
        });
    }

    observeEnvironment() {
        const observations = [
            '你注意到办公室有两个出口。',
            '主管的桌子上放着一串钥匙。',
            '墙上的监控摄像头似乎有盲区。',
            '窗户是锁着的，但锁看起来不太牢固。'
        ];

        const randomObservation = observations[Math.floor(Math.random() * observations.length)];

        dialogManager.show({
            text: randomObservation,
            choices: [{ text: '记住这个信息', action: () => this.addObservation(randomObservation) }]
        });
    }

    addObservation(observation) {
        const observations = this.getFlag('observations') || [];
        observations.push(observation);
        this.setFlag('observations', observations);

        audioManager.playSound('success');
    }

    reportProgress() {
        const performance = this.performance;
        let response;

        if (performance >= 10) {
            response = {
                text: '很好！继续保持这个水平。',
                effects: { suspicion: -2 }
            };
        } else if (performance >= 5) {
            response = {
                text: '还可以，但需要更努力一些。',
                effects: { suspicion: 0 }
            };
        } else {
            response = {
                text: '你的表现让我很失望！再不努力就滚蛋！',
                effects: { suspicion: 3 }
            };
        }

        dialogManager.show({
            speaker: '主管',
            text: response.text,
            choices: [{
                text: '我会努力的',
                effects: response.effects
            }]
        });
    }

    canLeaveDuringWork() {
        const hour = this.hour;
        const suspicion = this.suspicion;

        // 工作时间且怀疑值不高时可以离开
        return (hour >= 12 && hour <= 13) || (hour >= 18) || suspicion < 30;
    }

    contactFamily() {
        dialogManager.show({
            text: '你拨通了家人的电话，但只能简单地报平安，不敢说太多...',
            choices: [
                {
                    text: '告诉他们你的处境',
                    effects: { suspicion: 8 },
                    action: () => this.setFlag('family_knows_truth', true)
                },
                {
                    text: '只是简单聊聊',
                    effects: { suspicion: 2 }
                },
                {
                    text: '挂断电话',
                    effects: { suspicion: 1 }
                }
            ]
        });
    }

    checkNews() {
        const newsItems = [
            '当地警方加强了对诈骗团伙的打击力度。',
            '有受害者家属正在寻找失踪的亲人。',
            '国际社会对跨境诈骗问题表示关注。'
        ];

        const randomNews = newsItems[Math.floor(Math.random() * newsItems.length)];

        dialogManager.show({
            text: `新闻：${randomNews}`,
            choices: [{ text: '关闭', action: null }]
        });
    }

    observeOutside() {
        const observations = [
            '你看到围墙外有一条小路通向远处的村庄。',
            '围墙上有铁丝网，但有一处看起来松动了。',
            '你注意到保安的巡逻路线和时间规律。',
            '远处有一辆货车停在路边，可能是逃跑的机会。'
        ];

        const randomObservation = observations[Math.floor(Math.random() * observations.length)];

        dialogManager.show({
            text: randomObservation,
            choices: [{
                text: '记住这个信息',
                action: () => {
                    this.addObservation(randomObservation);
                    this.changeSuspicion(-1);
                }
            }]
        });
    }

    openWindow() {
        dialogManager.show({
            text: '你用螺丝刀撬开了窗户锁。现在可以从这里逃跑了！',
            choices: [
                {
                    text: '立即逃跑',
                    action: () => this.attemptEscape('window')
                },
                {
                    text: '等待更好的时机',
                    action: () => {
                        this.setFlag('window_unlocked', true);
                        audioManager.playSound('success');
                    }
                }
            ]
        });
    }

    attemptEscape(method) {
        let successChance = 0.3; // 基础成功率

        // 根据逃跑方法调整成功率
        switch (method) {
            case 'window':
                successChance = 0.6;
                break;
            case 'force':
                successChance = 0.2;
                break;
            case 'stealth':
                successChance = 0.8;
                break;
        }

        // 根据怀疑值调整成功率
        successChance -= (this.suspicion / 100) * 0.5;

        // 根据收集的信息调整成功率
        const observations = this.getFlag('observations') || [];
        successChance += observations.length * 0.1;

        if (Math.random() < successChance) {
            this.escapeSuccess();
        } else {
            this.escapeFailed();
        }
    }

    escapeSuccess() {
        this.setFlag('escaped_successfully', true);
        audioManager.stopMusic();
        audioManager.playSound('success');

        dialogManager.show({
            text: '你成功逃出了缅北园区！自由的空气从未如此甜美...',
            choices: [{
                text: '继续',
                action: () => this.triggerGameEnd('escaped')
            }]
        });
    }

    escapeFailed() {
        this.changeSuspicion(20);
        audioManager.playSound('error');
        graphics.shake(10, 2);

        dialogManager.show({
            text: '你被发现了！保安们正在追赶你...',
            choices: [
                {
                    text: '继续逃跑',
                    action: () => this.attemptEscape('force')
                },
                {
                    text: '投降',
                    action: () => this.triggerGameEnd('caught')
                }
            ]
        });
    }

    waitForOpportunity() {
        const hour = this.hour;
        let opportunity = false;

        if (hour >= 2 && hour <= 4) {
            opportunity = true;
        }

        if (opportunity) {
            dialogManager.show({
                text: '你等到了换班的时机，保安们注意力分散了。',
                choices: [{
                    text: '悄悄溜出去',
                    action: () => this.attemptEscape('stealth')
                }]
            });
        } else {
            dialogManager.show({
                text: '现在不是好时机，保安们很警觉。',
                choices: [{
                    text: '继续等待',
                    action: () => {
                        this.advanceTime(1);
                        this.waitForOpportunity();
                    }
                }]
            });
        }
    }

    findAlternativeExit() {
        if (this.getFlag('knows_escape_route')) {
            dialogManager.show({
                text: '你想起了室友告诉你的秘密通道...',
                choices: [{
                    text: '使用秘密通道',
                    action: () => this.attemptEscape('stealth')
                }]
            });
        } else {
            dialogManager.show({
                text: '你需要更多信息才能找到其他出路。',
                choices: [{ text: '返回', action: () => {} }]
            });
        }
    }

    distractGuard() {
        inventoryManager.removeItem('stone');
        this.setFlag('guard_distracted', true);

        dialogManager.show({
            text: '你扔出石头制造了噪音，保安被吸引走了。',
            choices: [{
                text: '趁机逃跑',
                action: () => this.attemptEscape('stealth')
            }]
        });
    }

    bribeGuard() {
        inventoryManager.removeItem('money', 500);
        this.setFlag('guard_bribed', true);

        dialogManager.show({
            text: '保安收下了钱，假装没看见你。',
            choices: [{
                text: '离开',
                action: () => this.attemptEscape('stealth')
            }]
        });
    }

    triggerBadEvent(eventType) {
        switch (eventType) {
            case 'supervisor_angry':
                this.changeSuspicion(10);
                graphics.shake(8, 1);
                audioManager.playSound('error');
                break;
        }
    }

    showSettings() {
        // 显示设置界面的逻辑
        app.showSettings();
    }

    showEndingScreen(ending) {
        // 创建结局界面
        const endingScreen = document.createElement('div');
        endingScreen.className = 'ending-screen';
        endingScreen.innerHTML = `
            <div class="ending-content">
                <h2>${ending.title}</h2>
                <p>${ending.text}</p>
                <div class="ending-buttons">
                    <button onclick="location.reload()">重新开始</button>
                    <button onclick="app.showMainMenu()">返回主菜单</button>
                </div>
            </div>
        `;

        document.body.appendChild(endingScreen);
    }

    // 保存游戏状态
    saveState() {
        return {
            suspicion: this.suspicion,
            performance: this.performance,
            day: this.day,
            hour: this.hour,
            flags: Array.from(this.flags.entries()),
            settings: this.settings
        };
    }

    // 加载游戏状态
    loadState(state) {
        this.suspicion = state.suspicion || 30;
        this.performance = state.performance || 0;
        this.day = state.day || 1;
        this.hour = state.hour || 9;

        if (state.flags) {
            this.flags = new Map(state.flags);
        }

        if (state.settings) {
            this.settings = { ...this.settings, ...state.settings };
        }

        this.updateUI();
    }

    // 加载设置
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem('fleeing_game_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...settings };
            }
        } catch (error) {
            console.warn('加载设置失败:', error);
        }
    }

    // 保存设置
    saveSettings() {
        try {
            localStorage.setItem('fleeing_game_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('保存设置失败:', error);
        }
    }
}

// 创建全局游戏实例
window.game = new Game();
