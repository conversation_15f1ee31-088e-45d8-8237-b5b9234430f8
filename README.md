# 逃出缅北 - H5游戏

一个基于HTML5 Canvas的2D点击式剧情解谜游戏，讲述主角在缅北园区的逃脱故事。

## 游戏特色

- **沉浸式剧情**: 基于真实背景的紧张刺激故事
- **道德选择系统**: 每个选择都会影响怀疑值和游戏进程
- **多结局设计**: 根据玩家的选择和行为触发不同结局
- **道具收集与组合**: 收集和使用各种道具来帮助逃脱
- **动态音效**: 根据场景和情况播放不同的背景音乐和音效
- **移动端适配**: 完美支持手机和平板设备的触摸操作

## 游戏系统

### 核心机制
- **怀疑值系统**: 0-100的怀疑值决定NPC对你的态度和可用选项
- **业绩值系统**: 影响主管对你的评价和工作相关的剧情
- **时间系统**: 游戏内时间影响NPC的行为和可用的交互选项
- **道具系统**: 8个道具槽位，支持道具组合和使用

### 场景设计
1. **主菜单**: 游戏开始界面
2. **办公室**: 主要工作场所，包含电脑、主管桌等交互元素
3. **宿舍**: 休息场所，可以与室友交流获取信息
4. **走廊**: 连接各个区域的通道，有监控摄像头
5. **出口区域**: 最终的逃脱挑战区域

### 角色系统
- **主角**: 被困在园区的受害者
- **主管**: 严厉的管理者，监控员工行为
- **室友小王**: 可以提供帮助和信息的同伴
- **保安**: 负责园区安全的守卫

## 技术架构

### 前端技术
- **HTML5 Canvas**: 游戏渲染引擎
- **JavaScript ES6+**: 游戏逻辑实现
- **Web Audio API**: 音频播放和控制
- **CSS3**: UI样式和动画效果
- **LocalStorage**: 游戏存档系统

### 代码结构
```
├── index.html          # 主页面文件
├── js/
│   ├── main.js         # 应用程序入口
│   ├── game.js         # 游戏核心逻辑
│   ├── scenes.js       # 场景管理系统
│   ├── graphics.js     # 图形渲染管理器
│   ├── audio.js        # 音频管理器
│   ├── input.js        # 输入处理器
│   ├── inventory.js    # 道具系统
│   ├── dialog.js       # 对话系统
│   └── utils.js        # 工具函数库
├── assets/             # 游戏资源文件
├── docs/               # 设计文档
└── server.py           # 开发服务器
```

### 核心类设计
- **Game**: 游戏核心控制器
- **SceneManager**: 场景管理器
- **GraphicsManager**: 图形渲染管理器
- **AudioManager**: 音频管理器
- **InputManager**: 输入处理器
- **InventoryManager**: 道具管理器
- **DialogManager**: 对话管理器

## 运行方法

### 方法一：使用Python服务器（推荐）
```bash
# 确保已安装Python 3
python3 server.py

# 或指定端口
python3 server.py 8080
```

然后在浏览器中访问 `http://localhost:8080`

### 方法二：使用其他HTTP服务器
```bash
# 使用Node.js的http-server
npx http-server -p 8080

# 使用PHP内置服务器
php -S localhost:8080

# 使用Python内置服务器
python3 -m http.server 8080
```

## 游戏操作

### 桌面端
- **鼠标左键**: 点击交互
- **鼠标右键**: 查看详细信息（部分元素）
- **ESC键**: 暂停/恢复游戏
- **F1键**: 切换调试模式
- **数字键1-9**: 快速选择对话选项

### 移动端
- **单击**: 基本交互
- **双击**: 快速使用道具
- **长按**: 查看详细信息
- **拖拽**: 移动道具位置

## 游戏攻略提示

### 生存策略
1. **保持低调**: 避免引起过多注意，控制怀疑值
2. **收集信息**: 与NPC对话，观察环境，收集有用信息
3. **合理选择**: 每个选择都有后果，考虑长远影响
4. **道具利用**: 善用道具组合，创造逃脱机会
5. **时机把握**: 注意时间系统，选择合适的行动时机

### 结局条件
- **完美逃脱**: 怀疑值低，收集足够信息，选择正确路线
- **惊险逃脱**: 怀疑值中等，通过冒险行动成功逃脱
- **被发现**: 怀疑值过高，行为暴露，被抓获
- **牺牲结局**: 为了保护他人或坚持原则而选择牺牲

## 开发信息

### 版本历史
- **v1.0.0**: 初始版本，包含完整的游戏流程和核心功能

### 技术要求
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **设备**: 支持HTML5 Canvas和Web Audio API
- **网络**: 需要HTTP服务器环境（不能直接打开HTML文件）

### 性能优化
- 资源预加载和缓存
- 图片压缩和格式优化
- 音频文件压缩
- 代码分割和懒加载
- 移动端适配优化

## 故障排除

### 常见问题
1. **游戏无法加载**: 确保使用HTTP服务器运行，不要直接打开HTML文件
2. **音频无法播放**: 检查浏览器音频权限设置
3. **图片显示异常**: 确保资源文件路径正确
4. **触摸操作无响应**: 检查移动端浏览器兼容性
5. **存档丢失**: 检查浏览器LocalStorage设置

### 调试模式
按F1键开启调试模式，可以查看：
- 实时FPS显示
- 当前场景信息
- 游戏状态数据
- 交互区域高亮
- 控制台日志输出

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址: [GitHub仓库链接]
- 邮箱: [开发者邮箱]

---

**注意**: 本游戏内容纯属虚构，旨在提高人们对相关社会问题的认识和防范意识。
