### **游戏规划文档：[逃出缅北]**

#### **1. 游戏名称**
*   **中文名:** 逃出缅北
*   **英文名:** (建议) Escape from the North / No Way Back

#### **2. 游戏概念 (Game Concept)**
一款 2D 点击式（Point-and-Click）剧情解谜游戏。玩家扮演一名被高薪工作诱骗至缅北诈骗园区的大学毕业生，必须在被严密监控、充满危险的环境中，利用智慧和勇气，通过观察环境、收集道具、解决谜题、与特定NPC（非玩家角色）互动，最终找到逃离园区的路线和方法。

*   **核心体验:** 紧张感、沉浸感、成就感。
*   **目标玩家:** 喜欢剧情解谜、密室逃脱类游戏的玩家，以及对现实题材感兴趣的群体。
*   **参考风格:**
    *   **玩法:** 类似“锈湖(Rusty Lake)”系列、“逃出办公室”等点击解谜游戏。
    *   **氛围:** 类似电影《孤注一掷》所描绘的压抑、紧张、混乱且暗藏危机的环境。

#### **3. 核心玩法 (Core Gameplay)**
*   **场景探索:** 玩家可以在固定的 2D 场景中（如办公室、宿舍、食堂、禁闭室等）通过点击与环境互动。高亮显示可互动的物品或区域。
*   **道具系统:**
    *   **收集:** 点击获取场景中的关键道具（如工牌、钥匙、纸条、螺丝刀、未完成的“话术本”等）。
    *   **组合:** 在物品栏中，可以将两个或多个相关道具组合成新的、有用的道具（例如：将铁丝掰直用来捅锁芯）。
    *   **使用:** 将物品栏中的道具拖拽到场景中的特定位置使用，以触发事件或解决谜题。
*   **谜题设计 (Puzzle Design):**
    *   **逻辑谜题:** 基于数字、文字、图案的密码锁，需要从环境中寻找线索（如海报上的电话号码、电脑里的特定日期）。
    *   **环境谜题:** 利用场景中的机关或物理规律解决问题（如切断电源让监控失效、制造混乱引开守卫）。
    *   **信息差谜题:** 通过窃听、偷看文件等方式获取关键信息，用于解开后续的谜题或说服其他角色。
*   **NPC 互动:**
    *   可以与部分“同事”（同样是被困者）或看守进行有限的对话。
    *   对话选项会影响NPC的信任度，可能获得关键线索，也可能因说错话而增加“怀疑值”。
*   **“怀疑值”系统:**
    *   一个时刻存在的风险系统。玩家的某些错误行为（如被守卫发现、强行破解失败、对话选择不当）会提升怀疑值。
    *   怀疑值达到顶峰会导致惩罚，如被关禁闭（一个特殊的解谜关卡）或直接游戏失败（Bad Ending）。
*   **“业绩”/“良心”系统:**
    *   一个与“怀疑值”相辅相成的道德抉择系统。在被迫执行诈骗任务时，玩家需要做出选择。
    *   **选择坚守底线:** 通过故意犯错、暗示对方等方式拒绝完成诈骗。这会**提升“怀疑值”**，增加游戏难度和风险。
    *   **选择同流合污:** 成功完成诈骗“业绩”。这可以**降低“怀疑值”**或换取特殊资源，让生存变得更容易，但会导致“有污点的逃亡”或“良心的谴责”结局。

#### **4. 游戏剧情梗概 (Story Synopsis)**
*   **主角:** 李明（可自定义），一名对未来充满憧憬的普通大学毕业生，因轻信网络上的高薪“海外程序员/翻译”招聘，被骗至缅北某诈骗园区。
*   **开端:** 游戏从主角被没收护照和手机，第一次进入嘈杂、压抑的诈骗“办公室”开始。他被分配了电脑和“话术本”，并被告知“开单”任务。
*   **发展:** 主角逐渐意识到这里的真相，目睹了身边人的遭遇（有人被打、有人绝望）。他内心的恐惧和求生欲被激发，决定策划逃离。他开始利用工作间隙、吃饭、上厕所等一切机会，偷偷观察环境，寻找线索。
*   **高潮:** 主角联合了一位同样想逃跑的“同事”，制定了最终逃跑计划。计划将在一个月黑风高的夜晚实行，需要用到之前收集的所有道具和线索，依次通过办公室、宿舍楼、避开巡逻的守卫，最终到达园区的薄弱环节——一个废弃的仓库。
*   **结局:**
    *   **完美结局 (Good Ending):** 成功逃离，并将收集到的部分证据交给当地警方。
    *   **普通结局 (Normal Ending):** 仅自己成功逃离，但未能帮助他人或带出证据。
    *   **坏结局 (Bad Ending):** 计划败露，被抓住并面临更绝望的境地。
    *   **有污点的逃亡 (Tainted Escape):** 主角在园区内为求自保而成功诈骗，最终虽逃离并提供了证据，但因其犯罪行为受到了法律的制裁。
    *   **良心的谴责 (Guilty Conscience):** 主角完成了诈骗，并最终只身逃离。虽然未受到法律追究，但将永远背负罪恶感，活在良心的谴责之下。

#### **5. 游戏场景与关卡设计 (Scene & Level Design)**
游戏可以分为几个主要章节，每个章节对应一个核心区域：

*   **第一章：初入牢笼 - 办公室**
    *   **环境:** 密集的电脑、桌上散落的话术本、墙上挂着“奋斗”标语、巡逻的“主管”。
    *   **核心谜题:** 破解自己电脑的简单密码，找到隐藏的同事求助纸条；想办法拿到旁边工位的废弃工牌。
*   **第二章：绝望之所 - 宿舍与食堂**
    *   **环境:** 脏乱的上下铺、锁住的窗户、统一发放的生活用品。
    *   **核心谜题:** 在床板下发现前人留下的逃跑线索；通过帮助室友解决小麻烦换取一个关键道具（如一小段铁丝）；在食堂特定时间制造混乱，偷取厨师的钥匙。
*   **第三章：虎口拔牙 - 经理办公室**
    *   **环境:** 装修稍好但更显阴森，有保险柜、监控总机、人事档案。
    *   **核心谜题:** 需要通过引开守卫或在特定时间潜入。破解保险柜密码（线索在园区各处），拿到园区的地图和后门钥匙。
*   **第四章：黑夜狂奔 - 逃离之路**
    *   **环境:** 漆黑的走廊、探照灯下的围墙、堆满杂物的仓库。
    *   **核心谜题:** 组合使用所有道具，按照正确的顺序和时机行动。例如，用湿毛巾让某个摄像头短路，用撬棍打开仓库的后门，最终在天亮前逃出。

#### **6. 美术与音效风格 (Art & Audio Style)**
*   **美术:**
    *   **风格:** 2D手绘，写实但略带压抑的风格。
    *   **色调:** 以暗色调为主，如深蓝、灰、暗绿，配合刺眼的日光灯或昏黄的灯泡，营造压抑、紧张的氛围。
    *   **UI:** 简洁明了，物品栏和对话框设计要符合整体的“粗糙”、“临时”感。
*   **音效:**
    *   **背景音乐 (BGM):** 以低沉、缓慢的环境音为主，在紧张环节（如躲避守卫）时切换为心跳声和急促的节奏。
    *   **音效 (SFX):** 键盘敲击声、电风扇转动声、守卫的脚步声、开锁声等，增强真实感和沉浸感。

#### **7. 技术实现初步构想 (Technical Implementation)**
*   **游戏引擎:**
    *   **Unity / Godot:** 功能强大，适合2D游戏开发，有丰富的社区和资源。Godot更为轻量，对独立开发者友好。
    *   **Web引擎 (如 Phaser.js):** 如果希望做成网页游戏，方便传播，这也是个不错的选择。
*   **发布平台:** PC (Steam, Itch.io), 移动端 (iOS/Android)。

#### **8. 下一步建议 (Next Steps)**
1.  **细化剧情:** 撰写详细的剧本，包括每个角色的背景故事和关键对话。
2.  **设计谜题:** 将每个关卡的谜题具体化，画出解谜的逻辑流程图。
3.  **美术概念设计:** 绘制主角、主要场景和关键道具的概念图，确定最终的美术风格。
4.  **开发原型:** 选择一个引擎，先实现一个核心场景（如办公室）和一个完整的谜题作为游戏原型（Prototype），以验证玩法的可行性和趣味性。
