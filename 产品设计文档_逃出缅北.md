# 《逃出缅北》产品设计文档

## 1. 产品概述

### 1.1 产品定位
- **类型：** 2D点击式剧情解谜游戏
- **平台：** 微信小游戏 (H5 Canvas)
- **目标用户：** 18-35岁，喜欢解谜游戏和现实题材的用户
- **核心体验：** 紧张刺激的逃脱体验，道德选择的心理冲击

### 1.2 核心游戏机制
- **点击交互系统：** 场景探索、道具收集、环境互动
- **道德选择系统：** 业绩/良心值影响游戏进程和结局
- **怀疑值系统：** 风险管理，影响游戏难度和生存状态
- **多结局系统：** 基于玩家选择的不同故事结局

## 2. 用户界面设计规范

### 2.1 整体UI布局 (适配微信小游戏)
```
┌─────────────────────────────────────┐
│  状态栏 (怀疑值/业绩值/时间)          │
├─────────────────────────────────────┤
│                                     │
│                                     │
│         主游戏场景区域                │
│        (720x960 适配竖屏)            │
│                                     │
│                                     │
├─────────────────────────────────────┤
│     道具栏 (可滑动，最多8个道具)      │
├─────────────────────────────────────┤
│  操作按钮区 (菜单/提示/设置)          │
└─────────────────────────────────────┘
```

### 2.2 交互元素设计
- **可交互物品：** 高亮边框 + 轻微发光效果
- **道具拖拽：** 支持触摸拖拽到目标位置
- **按钮反馈：** 点击缩放动画 + 触觉反馈
- **加载状态：** 进度条 + 背景故事文本

### 2.3 视觉层次设计
- **主要交互元素：** 高对比度，明显视觉引导
- **次要信息：** 低饱和度，不干扰主要操作
- **危险警告：** 红色系，闪烁或抖动效果
- **成功反馈：** 绿色系，粒子效果或光晕

## 3. 详细场景设计

### 3.1 开场引导场景

#### 场景描述
新手引导场景，模拟主角刚到达园区的第一天，通过简单交互让玩家熟悉基本操作。

#### 交互元素
- **护照和手机：** 点击触发被没收的剧情动画
- **工作证：** 拖拽到胸前佩戴，学习道具使用
- **电脑屏幕：** 点击查看"工作内容"，了解背景

#### 用户操作流程
1. 点击屏幕任意位置开始游戏
2. 观看简短的入园动画 (30秒)
3. 跟随高亮提示点击护照 → 触发没收剧情
4. 拖拽工作证到角色身上 → 学习拖拽操作
5. 点击电脑 → 查看第一个"工作任务"
6. 选择对话选项 → 了解道德选择系统

#### 反馈机制
- **操作成功：** 绿色勾选动画 + 轻微震动
- **操作错误：** 红色X动画 + 提示文本
- **进度提示：** 顶部进度条显示引导完成度

#### 转场逻辑
引导完成后，淡出到第一章开始界面，显示章节标题"初入牢笼"。

### 3.2 第一章：办公室场景

#### 场景描述
密集的工作区域，20多个工位，嘈杂的环境，墙上贴着激励标语，有巡逻的主管。

#### 交互元素详细设计

**主要交互区域：**
- **自己的工位 (中心区域)：**
  - 电脑屏幕：点击查看"话术本"和"客户信息"
  - 抽屉：点击打开，可能找到前人留下的纸条
  - 键盘：长按触发"假装工作"动画，降低怀疑值

- **邻座工位 (左侧)：**
  - 废弃工牌：需要在主管不注意时点击拿取
  - 垃圾桶：点击搜索，可能找到有用信息
  - 同事：点击对话，了解园区情况

- **主管巡逻路线 (动态元素)：**
  - 主管NPC每60秒巡逻一圈
  - 当主管接近时，屏幕边缘出现红色警告
  - 必须点击键盘"假装工作"避免怀疑值增加

- **墙面区域：**
  - 激励标语：点击查看，获得密码线索
  - 监控摄像头：点击观察，了解监控盲区
  - 时钟：显示游戏内时间，某些事件有时间限制

#### 核心谜题设计

**谜题1：破解电脑密码**
- **线索来源：** 墙上标语中的数字 + 邻座同事的生日
- **操作方式：** 点击数字键盘输入4位密码
- **失败惩罚：** 怀疑值+1，3次失败触发主管注意
- **成功奖励：** 获得"园区内部通讯录"道具

**谜题2：获取废弃工牌**
- **前置条件：** 观察主管巡逻规律
- **操作方式：** 在主管背对时点击工牌
- **时间限制：** 15秒窗口期
- **失败后果：** 怀疑值+2，被质问对话
- **成功奖励：** 获得"临时通行证"道具

#### 道德选择事件

**选择事件：第一次"开单"任务**
玩家必须选择如何处理分配的诈骗任务：

**选项A：拒绝执行 (坚守底线)**
- 操作：点击"挂断电话"按钮
- 后果：怀疑值+2，但良心值保持
- 反馈：屏幕轻微震动，主管皱眉动画

**选项B：执行诈骗 (同流合污)**
- 操作：按照话术本点击对话选项
- 后果：怀疑值-1，但触发"污点"标记
- 反馈：金币动画，但背景音乐变得沉重

**选项C：暗中破坏 (智慧选择)**
- 操作：故意输错信息或给出暗示
- 后果：怀疑值+1，但获得"智慧点数"
- 反馈：微妙的正面音效，NPC好感度提升

#### 用户操作流程
1. **环境观察阶段 (2-3分钟)：**
   - 点击各个区域了解环境
   - 观察主管巡逻规律
   - 与同事对话获取信息

2. **谜题解决阶段 (5-8分钟)：**
   - 收集密码线索
   - 等待时机获取工牌
   - 破解电脑获取信息

3. **道德选择阶段 (1-2分钟)：**
   - 面对第一个诈骗任务
   - 做出关键道德选择
   - 承受选择后果

4. **章节结束 (1分钟)：**
   - 查看获得的道具和信息
   - 显示当前怀疑值和业绩值
   - 过渡到下一章节

#### 反馈机制
- **成功操作：** 道具获得动画 + 音效
- **风险警告：** 屏幕边缘红色闪烁 + 心跳音效
- **时间压力：** 倒计时数字 + 紧张背景音乐
- **道德冲突：** 屏幕轻微抖动 + 沉重音效

#### 转场逻辑
章节结束时，显示"一天工作结束"文本，淡出到宿舍场景，同时显示当天的"工作总结"界面。

### 3.3 第二章：宿舍与食堂场景

#### 场景描述
上下铺宿舍，8人一间，窗户被铁栅栏封住，统一的生活用品，压抑的居住环境。

#### 交互元素设计

**宿舍区域：**
- **自己的床位 (下铺)：**
  - 枕头：点击掀开，查找隐藏物品
  - 床板：长按撬开，发现前人留下的逃跑线索
  - 行李箱：点击打开，整理有限的个人物品

- **室友床位：**
  - 室友A (上铺)：点击对话，可能获得小道具
  - 室友B (对面)：需要帮助解决问题才会提供帮助
  - 空床位：搜索前住户留下的线索

- **公共区域：**
  - 洗漱台：点击使用，可能发现隐藏信息
  - 垃圾桶：搜索废弃物品
  - 门锁：观察锁的类型，为后续开锁做准备

**食堂区域：**
- **取餐区：**
  - 餐具：选择合适的餐具 (某些可作为工具)
  - 厨师：观察其行为模式，寻找接近机会
  - 后厨门：需要特定时机才能进入

- **用餐区：**
  - 不同桌子的对话：点击偷听获取信息
  - 垃圾回收：制造混乱的机会
  - 监控盲区：寻找不被监视的位置

#### 核心谜题设计

**谜题1：床板下的秘密**
- **操作方式：** 连续点击床板边缘5次撬开
- **线索内容：** 手绘的园区地图 + 逃跑路线标记
- **获得道具：** "不完整地图"
- **风险：** 操作时不能被室友发现

**谜题2：帮助室友换取道具**
- **任务内容：** 室友的充电器坏了，需要修理
- **解决方案：** 使用之前获得的工牌中的小螺丝刀
- **操作方式：** 拖拽工具到充电器上
- **奖励：** 获得"铁丝"道具

**谜题3：食堂混乱计划**
- **目标：** 在特定时间制造混乱，偷取厨师钥匙
- **前置条件：** 观察厨师行为模式 (需要3个周期)
- **执行操作：** 
  1. 点击垃圾桶"意外"倾倒
  2. 在厨师清理时点击钥匙
  3. 在被发现前离开现场
- **时间限制：** 20秒完成整个操作
- **失败后果：** 怀疑值大幅增加

#### 社交互动系统

**室友关系管理：**
- **信任度系统：** 通过帮助室友提升信任度
- **信息交换：** 高信任度解锁更多对话选项
- **合作机会：** 某些任务需要室友配合

**对话选择影响：**
- **同情选项：** 提升NPC好感，可能获得帮助
- **冷漠选项：** 降低风险，但失去合作机会
- **激进选项：** 可能获得关键信息，但增加怀疑值

#### 用户操作流程
1. **适应环境 (3-4分钟)：**
   - 探索宿舍各个角落
   - 与室友建立初步关系
   - 了解作息时间和规则

2. **信息收集 (4-6分钟)：**
   - 搜索床板下的线索
   - 观察食堂环境和人员
   - 收集可用的小道具

3. **社交建立 (3-5分钟)：**
   - 帮助室友解决问题
   - 参与小组对话
   - 建立信任关系

4. **行动执行 (2-3分钟)：**
   - 执行食堂偷钥匙计划
   - 处理突发情况
   - 安全撤离

#### 反馈机制
- **信任度变化：** NPC头顶显示好感度图标变化
- **时间管理：** 屏幕角落显示作息时间提醒
- **风险提示：** 危险操作前显示确认对话框
- **成就解锁：** 完成隐藏任务时的特殊动画

#### 转场逻辑
第二章结束时，显示"深夜时分"，主角躺在床上整理收集到的线索，为下一步计划做准备。

### 3.4 游戏系统设计

#### 怀疑值系统详细机制
- **初始值：** 30/100 (中等警戒状态)
- **增加因素：**
  - 被发现可疑行为：+5-10
  - 拒绝工作任务：+3-5
  - 与同事过度交流：+2-3
  - 在禁区逗留：+8-15

- **减少因素：**
  - 成功完成诈骗任务：-5-8
  - 表现积极配合：-2-3
  - 举报他人：-10-15

- **阈值效果：**
  - 0-30：自由度高，行动限制少
  - 31-60：中等监管，某些区域限制
  - 61-80：高度警戒，行动严重受限
  - 81-100：触发"禁闭"特殊关卡

#### 道具组合系统
- **基础道具：** 工牌、铁丝、钥匙、纸条等
- **组合规则：** 拖拽道具到另一道具上尝试组合
- **成功组合：** 
  - 铁丝 + 工牌 = 简易撬锁工具
  - 纸条 + 笔 = 伪造文件
  - 钥匙 + 地图 = 完整逃跑计划

- **失败组合：** 显示"这两样东西无法组合"提示

#### 多结局分支系统
- **完美结局：** 怀疑值<40，业绩值=0，收集所有证据
- **普通结局：** 怀疑值<60，业绩值<30，部分证据
- **污点结局：** 业绩值>50，成功逃离但需承担后果
- **失败结局：** 怀疑值>80，被发现并面临惩罚
- **牺牲结局：** 帮助他人逃离，自己被抓

### 3.5 第三章：经理办公室场景

#### 场景描述
装修较好但氛围阴森的管理区域，有保险柜、监控总机、人事档案柜，是整个园区的信息中枢。

#### 交互元素设计

**主要区域：**
- **经理办公桌：**
  - 电脑：需要密码登录，包含重要信息
  - 抽屉：上锁，需要撬锁或找钥匙
  - 文件夹：点击查看，可能包含园区布局图

- **保险柜区域：**
  - 数字密码锁：需要输入6位密码
  - 密码线索：分散在园区各处，需要综合分析
  - 内容物：园区地图、后门钥匙、重要证据

- **监控系统：**
  - 监控屏幕：点击查看各区域实时画面
  - 控制面板：可以短暂关闭特定摄像头
  - 录像设备：获取证据的重要来源

#### 核心谜题设计

**谜题1：潜入时机选择**
- **观察阶段：** 需要在前面章节观察经理的作息规律
- **执行条件：** 经理离开办公室的30分钟窗口
- **风险管理：** 门外有守卫，需要分散注意力
- **操作方式：** 选择合适的潜入路线和时机

**谜题2：保险柜密码破解**
- **线索收集：** 6个数字分别来自：
  1. 办公室墙上的奖状日期
  2. 经理桌上的家庭照片
  3. 第一章电脑中的通讯录
  4. 第二章食堂菜单上的特殊标记
  5. 监控屏幕显示的时间戳
  6. 人事档案中的特定编号
- **输入机制：** 点击数字按键，错误3次触发警报
- **成功奖励：** 获得完整园区地图和后门钥匙

#### 高风险操作设计
- **守卫分散：** 需要使用之前获得的道具制造声响
- **监控规避：** 利用监控盲区移动，或短暂关闭摄像头
- **时间压力：** 整个潜入过程限时30分钟游戏时间
- **应急预案：** 如果被发现，需要快速隐藏或找借口

### 3.6 第四章：逃离之路场景

#### 场景描述
夜晚的园区，包括宿舍楼走廊、围墙区域、废弃仓库，最终的逃脱路线充满危险和挑战。

#### 多阶段逃离设计

**阶段1：深夜准备 (宿舍)**
- **时间设定：** 凌晨2点，大部分人已睡觉
- **准备工作：** 整理所有道具，制定最终计划
- **风险因素：** 室友可能醒来，夜班守卫巡逻

**阶段2：穿越走廊**
- **环境特点：** 昏暗的灯光，定时巡逻的守卫
- **操作要求：** 利用阴影移动，避开探照灯
- **道具使用：** 使用湿毛巾短路特定摄像头

**阶段3：翻越围墙**
- **物理挑战：** 需要组合使用绳索和撬棍
- **时间限制：** 探照灯每60秒扫描一次
- **技巧操作：** 精确的时机把握和路线选择

**阶段4：仓库逃脱**
- **最终谜题：** 使用所有收集的钥匙和工具
- **多路径选择：** 不同的逃脱路线对应不同结局
- **道德最终选择：** 是否回头帮助其他被困者

#### 结局触发机制
根据玩家在整个游戏过程中的选择和表现，触发不同的结局动画和文本。

## 4. 用户体验优化设计

### 4.1 新手引导系统
- **渐进式教学：** 每个新机制都有专门的引导
- **可跳过设计：** 老玩家可以跳过已知内容
- **错误容忍：** 新手操作错误有额外的提示和帮助

### 4.2 无障碍设计
- **视觉辅助：** 重要元素有明显的视觉标识
- **操作简化：** 复杂操作提供简化选项
- **文字大小：** 支持文字大小调节

### 4.3 性能优化
- **资源预加载：** 关键资源提前加载
- **内存管理：** 及时释放不需要的资源
- **网络优化：** 支持离线游戏，减少网络依赖

## 5. 数据统计与分析

### 5.1 关键指标定义
- **完成率：** 各章节的完成百分比
- **选择分布：** 不同道德选择的玩家比例
- **难点识别：** 玩家卡关最多的谜题
- **时长统计：** 各章节的平均游戏时长

### 5.2 用户行为分析
- **操作热力图：** 玩家点击最多的区域
- **路径分析：** 玩家的操作序列和习惯
- **错误统计：** 常见的操作错误和误解

---

**文档版本：** v1.0
**最后更新：** 2025-07-19
**负责人：** 产品经理
